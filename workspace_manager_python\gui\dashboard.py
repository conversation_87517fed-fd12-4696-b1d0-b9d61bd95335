"""
لوحة التحكم الرئيسية
Dashboard for Work Space Manager
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
from utils.config import COLORS, FONTS
from utils.helpers import format_currency, format_datetime

class DashboardFrame:
    """إطار لوحة التحكم"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        self.create_dashboard()
        self.refresh_data()
    
    def create_dashboard(self):
        """إنشاء لوحة التحكم"""
        # عنوان لوحة التحكم
        title_label = ttk.Label(
            self.frame, 
            text="📊 لوحة التحكم الرئيسية", 
            font=FONTS['title']
        )
        title_label.pack(pady=10)
        
        # إطار الإحصائيات السريعة
        self.create_stats_section()
        
        # إطار الجلسات النشطة
        self.create_active_sessions_section()
        
        # إطار الأنشطة الحديثة
        self.create_recent_activities_section()
        
        # زر التحديث
        refresh_btn = ttk.Button(
            self.frame, 
            text="🔄 تحديث البيانات", 
            command=self.refresh_data
        )
        refresh_btn.pack(pady=10)
    
    def create_stats_section(self):
        """إنشاء قسم الإحصائيات السريعة"""
        stats_frame = ttk.LabelFrame(self.frame, text="📈 الإحصائيات اليومية", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # إطار للبطاقات
        cards_frame = ttk.Frame(stats_frame)
        cards_frame.pack(fill=tk.X)
        
        # بطاقة إجمالي المبيعات
        self.sales_card = self.create_stat_card(
            cards_frame, "💰 إجمالي المبيعات", "0.00 ريال", COLORS['success']
        )
        self.sales_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة عدد الجلسات
        self.sessions_card = self.create_stat_card(
            cards_frame, "⏰ عدد الجلسات", "0", COLORS['primary']
        )
        self.sessions_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة العملاء الفريدين
        self.customers_card = self.create_stat_card(
            cards_frame, "👥 العملاء اليوم", "0", COLORS['info']
        )
        self.customers_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة الجلسات النشطة
        self.active_card = self.create_stat_card(
            cards_frame, "🔴 جلسات نشطة", "0", COLORS['warning']
        )
        self.active_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
    
    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk.Frame(parent, relief=tk.RAISED, borderwidth=1)
        
        # عنوان البطاقة
        title_label = ttk.Label(
            card_frame, 
            text=title, 
            font=FONTS['default'],
            foreground=color
        )
        title_label.pack(pady=5)
        
        # قيمة البطاقة
        value_label = ttk.Label(
            card_frame, 
            text=value, 
            font=FONTS['heading']
        )
        value_label.pack(pady=5)
        
        # حفظ مرجع للتحديث لاحقاً
        card_frame.value_label = value_label
        
        return card_frame
    
    def create_active_sessions_section(self):
        """إنشاء قسم الجلسات النشطة"""
        sessions_frame = ttk.LabelFrame(self.frame, text="🔴 الجلسات النشطة", padding=10)
        sessions_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # جدول الجلسات النشطة
        columns = {
            'id': {'text': 'رقم الجلسة', 'width': 80},
            'customer': {'text': 'العميل', 'width': 150},
            'start_time': {'text': 'وقت البداية', 'width': 120},
            'duration': {'text': 'المدة', 'width': 100},
            'rate': {'text': 'سعر الساعة', 'width': 80},
            'current_cost': {'text': 'التكلفة الحالية', 'width': 100}
        }
        
        self.active_sessions_tree = ttk.Treeview(sessions_frame)
        self.setup_treeview(self.active_sessions_tree, columns)
        
        # شريط التمرير
        sessions_scrollbar = ttk.Scrollbar(sessions_frame, orient=tk.VERTICAL, command=self.active_sessions_tree.yview)
        self.active_sessions_tree.configure(yscrollcommand=sessions_scrollbar.set)
        
        self.active_sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sessions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار الإجراءات
        actions_frame = ttk.Frame(sessions_frame)
        actions_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(
            actions_frame, 
            text="⏹️ إنهاء الجلسة", 
            command=self.end_selected_session
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            actions_frame, 
            text="🛍️ إضافة مشترى", 
            command=self.add_purchase_to_session
        ).pack(side=tk.LEFT, padx=5)
    
    def create_recent_activities_section(self):
        """إنشاء قسم الأنشطة الحديثة"""
        activities_frame = ttk.LabelFrame(self.frame, text="📋 الأنشطة الحديثة", padding=10)
        activities_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # قائمة الأنشطة
        self.activities_listbox = tk.Listbox(activities_frame, height=6)
        self.activities_listbox.pack(fill=tk.BOTH, expand=True)
        
        # شريط التمرير للأنشطة
        activities_scrollbar = ttk.Scrollbar(activities_frame, orient=tk.VERTICAL, command=self.activities_listbox.yview)
        self.activities_listbox.configure(yscrollcommand=activities_scrollbar.set)
        activities_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_treeview(self, treeview, columns):
        """إعداد جدول البيانات"""
        treeview['columns'] = list(columns.keys())
        treeview['show'] = 'headings'
        
        for col_id, col_info in columns.items():
            treeview.heading(col_id, text=col_info['text'])
            treeview.column(col_id, width=col_info['width'], anchor='center')
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.update_daily_stats()
        self.update_active_sessions()
        self.update_recent_activities()
    
    def update_daily_stats(self):
        """تحديث الإحصائيات اليومية"""
        try:
            stats = self.db.get_daily_stats()
            
            # تحديث بطاقة المبيعات
            self.sales_card.value_label.config(text=format_currency(stats['total_sales']))
            
            # تحديث بطاقة الجلسات
            self.sessions_card.value_label.config(text=str(stats['session_count']))
            
            # تحديث بطاقة العملاء
            self.customers_card.value_label.config(text=str(stats['unique_customers']))
            
            # تحديث بطاقة الجلسات النشطة
            active_sessions = self.db.get_active_sessions()
            self.active_card.value_label.config(text=str(len(active_sessions)))
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    def update_active_sessions(self):
        """تحديث الجلسات النشطة"""
        try:
            # مسح البيانات الحالية
            for item in self.active_sessions_tree.get_children():
                self.active_sessions_tree.delete(item)
            
            # الحصول على الجلسات النشطة
            active_sessions = self.db.get_active_sessions()
            
            for session in active_sessions:
                # حساب المدة الحالية
                now = datetime.now()
                duration = now - session.start_time
                duration_str = f"{int(duration.total_seconds() // 3600)}:{int((duration.total_seconds() % 3600) // 60):02d}"
                
                # حساب التكلفة الحالية
                current_hours = duration.total_seconds() / 3600
                current_cost = current_hours * session.hourly_rate
                
                self.active_sessions_tree.insert('', 'end', values=(
                    session.id,
                    session.customer_name,
                    format_datetime(session.start_time),
                    duration_str,
                    format_currency(session.hourly_rate),
                    format_currency(current_cost)
                ))
                
        except Exception as e:
            print(f"خطأ في تحديث الجلسات النشطة: {e}")
    
    def update_recent_activities(self):
        """تحديث الأنشطة الحديثة"""
        try:
            self.activities_listbox.delete(0, tk.END)
            
            # إضافة أنشطة تجريبية (يمكن تطويرها لاحقاً)
            activities = [
                f"🕐 {datetime.now().strftime('%H:%M')} - تم بدء جلسة جديدة",
                f"🕐 {(datetime.now() - timedelta(minutes=15)).strftime('%H:%M')} - تم إضافة عميل جديد",
                f"🕐 {(datetime.now() - timedelta(minutes=30)).strftime('%H:%M')} - تم إنهاء جلسة",
                f"🕐 {(datetime.now() - timedelta(hours=1)).strftime('%H:%M')} - تم إنشاء فاتورة",
                f"🕐 {(datetime.now() - timedelta(hours=2)).strftime('%H:%M')} - تم إضافة منتج جديد"
            ]
            
            for activity in activities:
                self.activities_listbox.insert(tk.END, activity)
                
        except Exception as e:
            print(f"خطأ في تحديث الأنشطة: {e}")
    
    def end_selected_session(self):
        """إنهاء الجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            from utils.helpers import show_warning
            show_warning("يرجى اختيار جلسة لإنهائها")
            return
        
        item = self.active_sessions_tree.item(selection[0])
        session_id = item['values'][0]
        
        try:
            from utils.helpers import ask_confirmation, show_success
            if ask_confirmation("هل أنت متأكد من إنهاء هذه الجلسة؟"):
                self.db.end_session(session_id)
                show_success("تم إنهاء الجلسة بنجاح")
                self.refresh_data()
        except Exception as e:
            from utils.helpers import show_error
            show_error(f"خطأ في إنهاء الجلسة: {e}")
    
    def add_purchase_to_session(self):
        """إضافة مشترى للجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            from utils.helpers import show_warning
            show_warning("يرجى اختيار جلسة لإضافة مشترى إليها")
            return
        
        # هنا يمكن فتح نافذة إضافة مشترى
        from utils.helpers import show_info
        show_info("سيتم تطوير هذه الميزة قريباً")
