"""
لوحة التحكم الرئيسية
Dashboard for Work Space Manager
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
from utils.config import COLORS, FONTS
from utils.helpers import format_currency, format_datetime

class DashboardFrame:
    """إطار لوحة التحكم"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        self.create_dashboard()
        self.refresh_data()
    
    def create_dashboard(self):
        """إنشاء لوحة التحكم"""
        # عنوان لوحة التحكم
        title_label = ttk.Label(
            self.frame,
            text="📊 لوحة التحكم الرئيسية",
            font=FONTS['title']
        )
        title_label.pack(pady=10)

        # إطار الإضافة السريعة
        self.create_quick_add_section()

        # إطار العملاء المسجلين
        self.create_existing_customers_section()

        # إطار الإحصائيات السريعة
        self.create_stats_section()

        # إطار الجلسات النشطة
        self.create_active_sessions_section()

        # إطار الأنشطة الحديثة
        self.create_recent_activities_section()

        # زر التحديث
        refresh_btn = ttk.Button(
            self.frame,
            text="🔄 تحديث البيانات",
            command=self.refresh_data
        )
        refresh_btn.pack(pady=10)

    def create_quick_add_section(self):
        """إنشاء قسم الإضافة السريعة"""
        quick_frame = ttk.LabelFrame(self.frame, text="⚡ إضافة سريعة", padding=10)
        quick_frame.pack(fill=tk.X, padx=10, pady=5)

        # إطار الحقول
        fields_frame = ttk.Frame(quick_frame)
        fields_frame.pack(fill=tk.X)

        # اسم العميل
        ttk.Label(fields_frame, text="اسم العميل:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.quick_customer_name = tk.StringVar()
        self.quick_name_entry = ttk.Entry(fields_frame, textvariable=self.quick_customer_name, width=20)
        self.quick_name_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # رقم الهاتف
        ttk.Label(fields_frame, text="الهاتف:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.quick_customer_phone = tk.StringVar()
        self.quick_phone_entry = ttk.Entry(fields_frame, textvariable=self.quick_customer_phone, width=15)
        self.quick_phone_entry.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)

        # سعر الساعة
        ttk.Label(fields_frame, text="سعر الساعة:").grid(row=0, column=4, sticky=tk.W, padx=5, pady=5)
        self.quick_hourly_rate = tk.StringVar(value="15.0")
        self.quick_rate_entry = ttk.Entry(fields_frame, textvariable=self.quick_hourly_rate, width=10)
        self.quick_rate_entry.grid(row=0, column=5, sticky=tk.W, padx=5, pady=5)

        # أزرار الإجراءات
        actions_frame = ttk.Frame(fields_frame)
        actions_frame.grid(row=0, column=6, padx=20, pady=5)

        ttk.Button(
            actions_frame,
            text="➕ إضافة عميل",
            command=self.quick_add_customer
        ).pack(side=tk.LEFT, padx=2)

        ttk.Button(
            actions_frame,
            text="🚀 إضافة وبدء جلسة",
            command=self.quick_add_and_start_session
        ).pack(side=tk.LEFT, padx=2)

        # ربط Enter بالإضافة السريعة
        self.quick_name_entry.bind('<Return>', lambda e: self.quick_add_and_start_session())
        self.quick_phone_entry.bind('<Return>', lambda e: self.quick_add_and_start_session())
        self.quick_rate_entry.bind('<Return>', lambda e: self.quick_add_and_start_session())

    def create_existing_customers_section(self):
        """إنشاء قسم العملاء المسجلين"""
        customers_frame = ttk.LabelFrame(self.frame, text="👥 العملاء المسجلين - بدء جلسة سريعة", padding=10)
        customers_frame.pack(fill=tk.X, padx=10, pady=5)

        # إطار البحث
        search_frame = ttk.Frame(customers_frame)
        search_frame.pack(fill=tk.X, pady=5)

        ttk.Label(search_frame, text="🔍 البحث:").pack(side=tk.LEFT, padx=5)
        self.customer_search_var = tk.StringVar()
        self.customer_search_entry = ttk.Entry(search_frame, textvariable=self.customer_search_var, width=20)
        self.customer_search_entry.pack(side=tk.LEFT, padx=5)
        self.customer_search_entry.bind('<KeyRelease>', self.filter_customers)

        ttk.Button(
            search_frame,
            text="🔄 تحديث",
            command=self.refresh_customers_list
        ).pack(side=tk.RIGHT, padx=5)

        # إطار قائمة العملاء
        list_frame = ttk.Frame(customers_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # جدول العملاء
        columns = {
            'id': {'text': 'الرقم', 'width': 60},
            'name': {'text': 'اسم العميل', 'width': 150},
            'phone': {'text': 'الهاتف', 'width': 120},
            'last_visit': {'text': 'آخر زيارة', 'width': 120},
            'total_sessions': {'text': 'عدد الجلسات', 'width': 100},
            'status': {'text': 'الحالة', 'width': 100}
        }

        self.customers_tree = ttk.Treeview(list_frame, height=6)
        self.setup_treeview(self.customers_tree, columns)

        # شريط التمرير
        customers_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=customers_scrollbar.set)

        self.customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        customers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # أزرار الإجراءات
        actions_frame = ttk.Frame(customers_frame)
        actions_frame.pack(fill=tk.X, pady=5)

        ttk.Button(
            actions_frame,
            text="🚀 بدء جلسة",
            command=self.start_session_for_customer
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            actions_frame,
            text="📊 عرض التفاصيل",
            command=self.show_customer_details
        ).pack(side=tk.LEFT, padx=5)

        # تحميل العملاء
        self.refresh_customers_list()

    def setup_treeview(self, treeview, columns):
        """إعداد جدول البيانات"""
        treeview['columns'] = list(columns.keys())
        treeview['show'] = 'headings'

        for col_id, col_info in columns.items():
            treeview.heading(col_id, text=col_info['text'])
            treeview.column(col_id, width=col_info['width'], anchor='center')

    def refresh_customers_list(self):
        """تحديث قائمة العملاء"""
        try:
            # مسح البيانات الحالية
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # الحصول على العملاء
            customers = self.db.get_customers()

            for customer in customers:
                if customer.is_active:
                    # حساب عدد الجلسات (يمكن تطويره لاحقاً)
                    total_sessions = 0
                    last_visit = "لا توجد زيارات"

                    # تحديد الحالة
                    active_sessions = self.db.get_active_sessions()
                    has_active_session = any(s.customer_id == customer.id for s in active_sessions)
                    status = "جلسة نشطة" if has_active_session else "متاح"

                    self.customers_tree.insert('', 'end', values=(
                        customer.id,
                        customer.name,
                        customer.phone or "غير محدد",
                        last_visit,
                        total_sessions,
                        status
                    ))

        except Exception as e:
            print(f"خطأ في تحديث قائمة العملاء: {e}")

    def filter_customers(self, event=None):
        """فلترة العملاء حسب البحث"""
        search_term = self.customer_search_var.get().lower()

        # مسح البيانات الحالية
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)

        try:
            customers = self.db.get_customers()

            for customer in customers:
                if customer.is_active and (not search_term or search_term in customer.name.lower()):
                    # حساب عدد الجلسات
                    total_sessions = 0
                    last_visit = "لا توجد زيارات"

                    # تحديد الحالة
                    active_sessions = self.db.get_active_sessions()
                    has_active_session = any(s.customer_id == customer.id for s in active_sessions)
                    status = "جلسة نشطة" if has_active_session else "متاح"

                    self.customers_tree.insert('', 'end', values=(
                        customer.id,
                        customer.name,
                        customer.phone or "غير محدد",
                        last_visit,
                        total_sessions,
                        status
                    ))

        except Exception as e:
            print(f"خطأ في فلترة العملاء: {e}")

    def start_session_for_customer(self):
        """بدء جلسة للعميل المحدد"""
        selection = self.customers_tree.selection()
        if not selection:
            from utils.helpers import show_warning
            show_warning("يرجى اختيار عميل لبدء الجلسة")
            return

        item = self.customers_tree.item(selection[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]
        status = item['values'][5]

        if status == "جلسة نشطة":
            from utils.helpers import show_warning
            show_warning("يوجد جلسة نشطة لهذا العميل بالفعل")
            return

        try:
            # نافذة تحديد سعر الساعة
            dialog = SessionStartDialog(self.frame, "بدء جلسة جديدة", customer_name)
            if dialog.result:
                hourly_rate = dialog.result

                # بدء الجلسة
                session_id = self.db.start_session(customer_id, customer_name, hourly_rate)

                from utils.helpers import show_success
                show_success(f"تم بدء الجلسة رقم {session_id} للعميل '{customer_name}' بنجاح!")

                # تحديث البيانات
                self.refresh_data()

        except Exception as e:
            from utils.helpers import show_error
            show_error(f"خطأ في بدء الجلسة: {e}")

    def show_customer_details(self):
        """عرض تفاصيل العميل المحدد"""
        selection = self.customers_tree.selection()
        if not selection:
            from utils.helpers import show_warning
            show_warning("يرجى اختيار عميل لعرض التفاصيل")
            return

        from utils.helpers import show_info
        show_info("سيتم تطوير هذه الميزة قريباً")

    def create_stats_section(self):
        """إنشاء قسم الإحصائيات السريعة"""
        stats_frame = ttk.LabelFrame(self.frame, text="📈 الإحصائيات اليومية", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # إطار للبطاقات
        cards_frame = ttk.Frame(stats_frame)
        cards_frame.pack(fill=tk.X)
        
        # بطاقة إجمالي المبيعات
        self.sales_card = self.create_stat_card(
            cards_frame, "💰 إجمالي المبيعات", "0.00 ريال", COLORS['success']
        )
        self.sales_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة عدد الجلسات
        self.sessions_card = self.create_stat_card(
            cards_frame, "⏰ عدد الجلسات", "0", COLORS['primary']
        )
        self.sessions_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة العملاء الفريدين
        self.customers_card = self.create_stat_card(
            cards_frame, "👥 العملاء اليوم", "0", COLORS['info']
        )
        self.customers_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة الجلسات النشطة
        self.active_card = self.create_stat_card(
            cards_frame, "🔴 جلسات نشطة", "0", COLORS['warning']
        )
        self.active_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
    
    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk.Frame(parent, relief=tk.RAISED, borderwidth=1)
        
        # عنوان البطاقة
        title_label = ttk.Label(
            card_frame, 
            text=title, 
            font=FONTS['default'],
            foreground=color
        )
        title_label.pack(pady=5)
        
        # قيمة البطاقة
        value_label = ttk.Label(
            card_frame, 
            text=value, 
            font=FONTS['heading']
        )
        value_label.pack(pady=5)
        
        # حفظ مرجع للتحديث لاحقاً
        card_frame.value_label = value_label
        
        return card_frame
    
    def create_active_sessions_section(self):
        """إنشاء قسم الجلسات النشطة"""
        sessions_frame = ttk.LabelFrame(self.frame, text="🔴 الجلسات النشطة", padding=10)
        sessions_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # جدول الجلسات النشطة
        columns = {
            'id': {'text': 'رقم الجلسة', 'width': 80},
            'customer': {'text': 'العميل', 'width': 150},
            'start_time': {'text': 'وقت البداية', 'width': 120},
            'duration': {'text': 'المدة', 'width': 100},
            'rate': {'text': 'سعر الساعة', 'width': 80},
            'current_cost': {'text': 'التكلفة الحالية', 'width': 100}
        }
        
        self.active_sessions_tree = ttk.Treeview(sessions_frame)
        self.setup_treeview(self.active_sessions_tree, columns)
        
        # شريط التمرير
        sessions_scrollbar = ttk.Scrollbar(sessions_frame, orient=tk.VERTICAL, command=self.active_sessions_tree.yview)
        self.active_sessions_tree.configure(yscrollcommand=sessions_scrollbar.set)
        
        self.active_sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sessions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار الإجراءات
        actions_frame = ttk.Frame(sessions_frame)
        actions_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(
            actions_frame, 
            text="⏹️ إنهاء الجلسة", 
            command=self.end_selected_session
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            actions_frame, 
            text="🛍️ إضافة مشترى", 
            command=self.add_purchase_to_session
        ).pack(side=tk.LEFT, padx=5)
    
    def create_recent_activities_section(self):
        """إنشاء قسم الأنشطة الحديثة"""
        activities_frame = ttk.LabelFrame(self.frame, text="📋 الأنشطة الحديثة", padding=10)
        activities_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # قائمة الأنشطة
        self.activities_listbox = tk.Listbox(activities_frame, height=6)
        self.activities_listbox.pack(fill=tk.BOTH, expand=True)
        
        # شريط التمرير للأنشطة
        activities_scrollbar = ttk.Scrollbar(activities_frame, orient=tk.VERTICAL, command=self.activities_listbox.yview)
        self.activities_listbox.configure(yscrollcommand=activities_scrollbar.set)
        activities_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_treeview(self, treeview, columns):
        """إعداد جدول البيانات"""
        treeview['columns'] = list(columns.keys())
        treeview['show'] = 'headings'
        
        for col_id, col_info in columns.items():
            treeview.heading(col_id, text=col_info['text'])
            treeview.column(col_id, width=col_info['width'], anchor='center')
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.update_daily_stats()
        self.update_active_sessions()
        self.update_recent_activities()
    
    def update_daily_stats(self):
        """تحديث الإحصائيات اليومية"""
        try:
            stats = self.db.get_daily_stats()
            
            # تحديث بطاقة المبيعات
            self.sales_card.value_label.config(text=format_currency(stats['total_sales']))
            
            # تحديث بطاقة الجلسات
            self.sessions_card.value_label.config(text=str(stats['session_count']))
            
            # تحديث بطاقة العملاء
            self.customers_card.value_label.config(text=str(stats['unique_customers']))
            
            # تحديث بطاقة الجلسات النشطة
            active_sessions = self.db.get_active_sessions()
            self.active_card.value_label.config(text=str(len(active_sessions)))
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    def update_active_sessions(self):
        """تحديث الجلسات النشطة"""
        try:
            # مسح البيانات الحالية
            for item in self.active_sessions_tree.get_children():
                self.active_sessions_tree.delete(item)
            
            # الحصول على الجلسات النشطة
            active_sessions = self.db.get_active_sessions()
            
            for session in active_sessions:
                # حساب المدة الحالية
                now = datetime.now()
                duration = now - session.start_time
                duration_str = f"{int(duration.total_seconds() // 3600)}:{int((duration.total_seconds() % 3600) // 60):02d}"
                
                # حساب التكلفة الحالية
                current_hours = duration.total_seconds() / 3600
                current_cost = current_hours * session.hourly_rate
                
                self.active_sessions_tree.insert('', 'end', values=(
                    session.id,
                    session.customer_name,
                    format_datetime(session.start_time),
                    duration_str,
                    format_currency(session.hourly_rate),
                    format_currency(current_cost)
                ))
                
        except Exception as e:
            print(f"خطأ في تحديث الجلسات النشطة: {e}")
    
    def update_recent_activities(self):
        """تحديث الأنشطة الحديثة"""
        try:
            self.activities_listbox.delete(0, tk.END)
            
            # إضافة أنشطة تجريبية (يمكن تطويرها لاحقاً)
            activities = [
                f"🕐 {datetime.now().strftime('%H:%M')} - تم بدء جلسة جديدة",
                f"🕐 {(datetime.now() - timedelta(minutes=15)).strftime('%H:%M')} - تم إضافة عميل جديد",
                f"🕐 {(datetime.now() - timedelta(minutes=30)).strftime('%H:%M')} - تم إنهاء جلسة",
                f"🕐 {(datetime.now() - timedelta(hours=1)).strftime('%H:%M')} - تم إنشاء فاتورة",
                f"🕐 {(datetime.now() - timedelta(hours=2)).strftime('%H:%M')} - تم إضافة منتج جديد"
            ]
            
            for activity in activities:
                self.activities_listbox.insert(tk.END, activity)
                
        except Exception as e:
            print(f"خطأ في تحديث الأنشطة: {e}")
    
    def end_selected_session(self):
        """إنهاء الجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            from utils.helpers import show_warning
            show_warning("يرجى اختيار جلسة لإنهائها")
            return
        
        item = self.active_sessions_tree.item(selection[0])
        session_id = item['values'][0]
        
        try:
            from utils.helpers import ask_confirmation, show_success
            if ask_confirmation("هل أنت متأكد من إنهاء هذه الجلسة؟"):
                self.db.end_session(session_id)
                show_success("تم إنهاء الجلسة بنجاح")
                self.refresh_data()
        except Exception as e:
            from utils.helpers import show_error
            show_error(f"خطأ في إنهاء الجلسة: {e}")
    
    def add_purchase_to_session(self):
        """إضافة مشترى للجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            from utils.helpers import show_warning
            show_warning("يرجى اختيار جلسة لإضافة مشترى إليها")
            return

        item = self.active_sessions_tree.item(selection[0])
        session_id = item['values'][0]

        try:
            dialog = PurchaseDialog(self.frame, "إضافة مشترى", self.db, session_id)
            if dialog.result:
                from utils.helpers import show_success
                show_success("تم إضافة المشترى بنجاح")
                self.refresh_data()
        except Exception as e:
            from utils.helpers import show_error
            show_error(f"خطأ في إضافة المشترى: {e}")

    def quick_add_customer(self):
        """إضافة عميل سريع"""
        try:
            from database.models import Customer
            from utils.helpers import show_success, show_error, validate_phone

            name = self.quick_customer_name.get().strip()
            if not name:
                show_error("يرجى إدخال اسم العميل")
                return

            phone = self.quick_customer_phone.get().strip()
            if phone and not validate_phone(phone):
                show_error("رقم الهاتف غير صحيح")
                return

            # إنشاء العميل
            customer = Customer(
                name=name,
                phone=phone,
                email="",
                notes="تم إضافته من الإضافة السريعة"
            )

            customer_id = self.db.add_customer(customer)
            show_success(f"تم إضافة العميل '{name}' بنجاح")

            # مسح الحقول
            self.quick_customer_name.set("")
            self.quick_customer_phone.set("")

            # تحديث البيانات
            self.refresh_data()

            return customer_id

        except Exception as e:
            from utils.helpers import show_error
            show_error(f"خطأ في إضافة العميل: {e}")
            return None

    def quick_add_and_start_session(self):
        """إضافة عميل وبدء جلسة مباشرة"""
        try:
            from utils.helpers import show_success, show_error, safe_float_convert

            name = self.quick_customer_name.get().strip()
            if not name:
                show_error("يرجى إدخال اسم العميل")
                return

            hourly_rate = safe_float_convert(self.quick_hourly_rate.get(), 15.0)
            if hourly_rate <= 0:
                show_error("يرجى إدخال سعر ساعة صحيح")
                return

            # إضافة العميل أولاً
            customer_id = self.quick_add_customer()
            if not customer_id:
                return

            # بدء الجلسة
            session_id = self.db.start_session(customer_id, name, hourly_rate)
            show_success(f"تم بدء الجلسة رقم {session_id} للعميل '{name}' بنجاح!")

            # تحديث البيانات
            self.refresh_data()

        except Exception as e:
            from utils.helpers import show_error
            show_error(f"خطأ في بدء الجلسة: {e}")

    def check_expired_sessions(self):
        """التحقق من الجلسات المنتهية"""
        try:
            ended_sessions = self.db.check_and_end_expired_sessions()
            if ended_sessions:
                from utils.helpers import show_info
                session_names = [s.customer_name for s in ended_sessions]
                show_info(f"تم إنهاء {len(ended_sessions)} جلسة تلقائياً:\n" + "\n".join(session_names))
        except Exception as e:
            print(f"خطأ في التحقق من الجلسات المنتهية: {e}")


class PurchaseDialog:
    """نافذة حوار إضافة مشترى"""

    def __init__(self, parent, title, db, session_id):
        self.result = None
        self.db = db
        self.session_id = session_id

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x350")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_dialog()

        # إنشاء الواجهة
        self.create_interface()

        # تحميل المنتجات
        self.load_products()

    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 450) // 2
        y = (self.dialog.winfo_screenheight() - 350) // 2
        self.dialog.geometry(f"450x350+{x}+{y}")

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات الجلسة
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الجلسة", padding=10)
        info_frame.pack(fill=tk.X, pady=5)

        ttk.Label(info_frame, text=f"رقم الجلسة: {self.session_id}").pack(anchor=tk.W)

        # اختيار المنتج
        ttk.Label(main_frame, text="المنتج:").pack(anchor=tk.W, pady=(10, 0))
        self.product_var = tk.StringVar()
        self.product_combo = ttk.Combobox(main_frame, textvariable=self.product_var, state="readonly")
        self.product_combo.pack(fill=tk.X, pady=5)
        self.product_combo.bind('<<ComboboxSelected>>', self.on_product_select)

        # الكمية
        quantity_frame = ttk.Frame(main_frame)
        quantity_frame.pack(fill=tk.X, pady=10)

        ttk.Label(quantity_frame, text="الكمية:").pack(side=tk.LEFT)
        self.quantity_var = tk.StringVar(value="1")
        self.quantity_entry = ttk.Entry(quantity_frame, textvariable=self.quantity_var, width=10)
        self.quantity_entry.pack(side=tk.LEFT, padx=5)
        self.quantity_entry.bind('<KeyRelease>', self.calculate_total)

        # السعر
        ttk.Label(quantity_frame, text="السعر:").pack(side=tk.LEFT, padx=(20, 5))
        self.price_var = tk.StringVar()
        self.price_entry = ttk.Entry(quantity_frame, textvariable=self.price_var, width=10)
        self.price_entry.pack(side=tk.LEFT, padx=5)
        self.price_entry.bind('<KeyRelease>', self.calculate_total)

        # الإجمالي
        total_frame = ttk.Frame(main_frame)
        total_frame.pack(fill=tk.X, pady=10)

        ttk.Label(total_frame, text="الإجمالي:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.total_label = ttk.Label(total_frame, text="0.00 جنيه", font=('Arial', 10, 'bold'), foreground="green")
        self.total_label.pack(side=tk.LEFT, padx=10)

        # الملاحظات
        ttk.Label(main_frame, text="الملاحظات:").pack(anchor=tk.W, pady=(10, 0))
        self.notes_text = tk.Text(main_frame, height=3)
        self.notes_text.pack(fill=tk.X, pady=5)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="💾 إضافة المشترى", command=self.add_purchase).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.cancel).pack(side=tk.LEFT, padx=5)

    def load_products(self):
        """تحميل المنتجات"""
        try:
            products = self.db.get_products()
            product_options = []
            self.products_dict = {}

            for product in products:
                if product.is_active and product.quantity > 0:
                    option = f"{product.name} - {product.price:.2f} جنيه (متاح: {product.quantity})"
                    product_options.append(option)
                    self.products_dict[option] = product

            self.product_combo['values'] = product_options
            if product_options:
                self.product_combo.set(product_options[0])
                self.on_product_select()
        except Exception as e:
            print(f"خطأ في تحميل المنتجات: {e}")

    def on_product_select(self, event=None):
        """عند اختيار منتج"""
        selected = self.product_var.get()
        if selected in self.products_dict:
            product = self.products_dict[selected]
            self.price_var.set(str(product.price))
            self.calculate_total()

    def calculate_total(self, event=None):
        """حساب الإجمالي"""
        try:
            from utils.helpers import safe_float_convert, safe_int_convert
            quantity = safe_int_convert(self.quantity_var.get(), 1)
            price = safe_float_convert(self.price_var.get(), 0)
            total = quantity * price
            self.total_label.config(text=f"{total:.2f} جنيه")
        except:
            self.total_label.config(text="0.00 جنيه")

    def add_purchase(self):
        """إضافة المشترى"""
        try:
            from utils.helpers import safe_float_convert, safe_int_convert, show_error

            selected = self.product_var.get()
            if not selected or selected not in self.products_dict:
                show_error("يرجى اختيار منتج")
                return

            product = self.products_dict[selected]
            quantity = safe_int_convert(self.quantity_var.get(), 1)
            price = safe_float_convert(self.price_var.get(), 0)

            if quantity <= 0:
                show_error("يرجى إدخال كمية صحيحة")
                return

            if quantity > product.quantity:
                show_error(f"الكمية المطلوبة ({quantity}) أكبر من المتاح ({product.quantity})")
                return

            if price <= 0:
                show_error("يرجى إدخال سعر صحيح")
                return

            # إضافة المشترى
            purchase_id = self.db.add_purchase(
                session_id=self.session_id,
                product_id=product.id,
                product_name=product.name,
                quantity=quantity,
                unit_price=price,
                notes=self.notes_text.get(1.0, tk.END).strip()
            )

            self.result = purchase_id
            self.dialog.destroy()

        except Exception as e:
            from utils.helpers import show_error
            show_error(f"خطأ في إضافة المشترى: {e}")

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()


class SessionStartDialog:
    """نافذة حوار بدء جلسة"""

    def __init__(self, parent, title, customer_name):
        self.result = None
        self.customer_name = customer_name

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("350x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_dialog()

        # إنشاء الواجهة
        self.create_interface()

    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 350) // 2
        y = (self.dialog.winfo_screenheight() - 200) // 2
        self.dialog.geometry(f"350x200+{x}+{y}")

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات العميل
        info_frame = ttk.LabelFrame(main_frame, text="معلومات العميل", padding=10)
        info_frame.pack(fill=tk.X, pady=5)

        ttk.Label(info_frame, text=f"العميل: {self.customer_name}", font=('Arial', 10, 'bold')).pack(anchor=tk.W)

        # سعر الساعة
        rate_frame = ttk.Frame(main_frame)
        rate_frame.pack(fill=tk.X, pady=10)

        ttk.Label(rate_frame, text="سعر الساعة:").pack(side=tk.LEFT)
        self.hourly_rate_var = tk.StringVar(value="15.0")
        self.hourly_rate_entry = ttk.Entry(rate_frame, textvariable=self.hourly_rate_var, width=10)
        self.hourly_rate_entry.pack(side=tk.LEFT, padx=5)
        ttk.Label(rate_frame, text="جنيه").pack(side=tk.LEFT)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="🚀 بدء الجلسة", command=self.start_session).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.cancel).pack(side=tk.LEFT, padx=5)

        # التركيز على حقل السعر
        self.hourly_rate_entry.focus()
        self.hourly_rate_entry.select_range(0, tk.END)

    def start_session(self):
        """بدء الجلسة"""
        try:
            from utils.helpers import safe_float_convert, show_error

            hourly_rate = safe_float_convert(self.hourly_rate_var.get())
            if hourly_rate <= 0:
                show_error("يرجى إدخال سعر ساعة صحيح")
                return

            self.result = hourly_rate
            self.dialog.destroy()

        except Exception as e:
            from utils.helpers import show_error
            show_error(f"خطأ في بدء الجلسة: {e}")

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()
