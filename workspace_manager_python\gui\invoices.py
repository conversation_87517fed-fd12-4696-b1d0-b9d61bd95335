"""
واجهة إدارة الفواتير
Invoices Management Interface
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from utils.config import FONTS, INVOICE_SETTINGS
from utils.helpers import (
    show_success, show_error, show_warning, ask_confirmation,
    format_currency, format_datetime
)

class InvoicesFrame:
    """إطار إدارة الفواتير"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        self.create_interface()
        self.refresh_invoices()
    
    def create_interface(self):
        """إنشاء واجهة إدارة الفواتير"""
        # عنوان الصفحة
        title_label = ttk.Label(
            self.frame, 
            text="🧾 إدارة الفواتير", 
            font=FONTS['title']
        )
        title_label.pack(pady=10)
        
        # إطار البحث والفلاتر
        self.create_search_section()
        
        # إطار قائمة الفواتير
        self.create_invoices_list()
        
        # إطار تفاصيل الفاتورة
        self.create_invoice_details()
    
    def create_search_section(self):
        """إنشاء قسم البحث والفلاتر"""
        search_frame = ttk.Frame(self.frame)
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # فلتر التاريخ
        ttk.Label(search_frame, text="من تاريخ:").pack(side=tk.LEFT, padx=5)
        self.from_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.from_date_entry = ttk.Entry(search_frame, textvariable=self.from_date_var, width=12)
        self.from_date_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(search_frame, text="إلى تاريخ:").pack(side=tk.LEFT, padx=5)
        self.to_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.to_date_entry = ttk.Entry(search_frame, textvariable=self.to_date_var, width=12)
        self.to_date_entry.pack(side=tk.LEFT, padx=5)
        
        # البحث بالعميل
        ttk.Label(search_frame, text="العميل:").pack(side=tk.LEFT, padx=(20, 5))
        self.customer_search_var = tk.StringVar()
        self.customer_search_entry = ttk.Entry(search_frame, textvariable=self.customer_search_var, width=20)
        self.customer_search_entry.pack(side=tk.LEFT, padx=5)
        
        # أزرار الإجراءات
        buttons_frame = ttk.Frame(search_frame)
        buttons_frame.pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(
            buttons_frame, 
            text="🔍 بحث", 
            command=self.search_invoices
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="➕ فاتورة جديدة", 
            command=self.create_new_invoice
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="🖨️ طباعة", 
            command=self.print_invoice
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="🔄 تحديث", 
            command=self.refresh_invoices
        ).pack(side=tk.LEFT, padx=2)
    
    def create_invoices_list(self):
        """إنشاء قائمة الفواتير"""
        list_frame = ttk.LabelFrame(self.frame, text="قائمة الفواتير", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # جدول الفواتير
        columns = {
            'id': {'text': 'رقم الفاتورة', 'width': 100},
            'customer': {'text': 'العميل', 'width': 150},
            'date': {'text': 'التاريخ', 'width': 120},
            'session_amount': {'text': 'مبلغ الجلسة', 'width': 100},
            'purchases_amount': {'text': 'مبلغ المشتريات', 'width': 100},
            'total_amount': {'text': 'الإجمالي', 'width': 100},
            'discount': {'text': 'الخصم', 'width': 80},
            'final_amount': {'text': 'المبلغ النهائي', 'width': 100},
            'payment_method': {'text': 'طريقة الدفع', 'width': 100}
        }
        
        self.invoices_tree = ttk.Treeview(list_frame)
        self.setup_treeview(self.invoices_tree, columns)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)
        
        self.invoices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث التحديد
        self.invoices_tree.bind('<<TreeviewSelect>>', self.on_invoice_select)
    
    def create_invoice_details(self):
        """إنشاء قسم تفاصيل الفاتورة"""
        details_frame = ttk.LabelFrame(self.frame, text="تفاصيل الفاتورة", padding=10)
        details_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # معلومات الفاتورة
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X)
        
        # العمود الأول
        col1_frame = ttk.Frame(info_frame)
        col1_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(col1_frame, text="رقم الفاتورة:", font=FONTS['default']).pack(anchor=tk.W)
        self.invoice_id_label = ttk.Label(col1_frame, text="-", font=FONTS['heading'])
        self.invoice_id_label.pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Label(col1_frame, text="العميل:", font=FONTS['default']).pack(anchor=tk.W)
        self.customer_label = ttk.Label(col1_frame, text="-")
        self.customer_label.pack(anchor=tk.W, pady=(0, 10))
        
        # العمود الثاني
        col2_frame = ttk.Frame(info_frame)
        col2_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(col2_frame, text="التاريخ:", font=FONTS['default']).pack(anchor=tk.W)
        self.date_label = ttk.Label(col2_frame, text="-")
        self.date_label.pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Label(col2_frame, text="طريقة الدفع:", font=FONTS['default']).pack(anchor=tk.W)
        self.payment_method_label = ttk.Label(col2_frame, text="-")
        self.payment_method_label.pack(anchor=tk.W, pady=(0, 10))
        
        # العمود الثالث
        col3_frame = ttk.Frame(info_frame)
        col3_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(col3_frame, text="المبلغ النهائي:", font=FONTS['default']).pack(anchor=tk.W)
        self.final_amount_label = ttk.Label(col3_frame, text="-", font=FONTS['heading'], foreground="green")
        self.final_amount_label.pack(anchor=tk.W, pady=(0, 10))
        
        # تفاصيل المبالغ
        amounts_frame = ttk.LabelFrame(details_frame, text="تفاصيل المبالغ", padding=5)
        amounts_frame.pack(fill=tk.X, pady=10)
        
        amounts_info_frame = ttk.Frame(amounts_frame)
        amounts_info_frame.pack(fill=tk.X)
        
        # مبلغ الجلسة
        ttk.Label(amounts_info_frame, text="مبلغ الجلسة:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.session_amount_label = ttk.Label(amounts_info_frame, text="-")
        self.session_amount_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # مبلغ المشتريات
        ttk.Label(amounts_info_frame, text="مبلغ المشتريات:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.purchases_amount_label = ttk.Label(amounts_info_frame, text="-")
        self.purchases_amount_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # الإجمالي قبل الخصم
        ttk.Label(amounts_info_frame, text="الإجمالي:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.total_amount_label = ttk.Label(amounts_info_frame, text="-")
        self.total_amount_label.grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        
        # الخصم
        ttk.Label(amounts_info_frame, text="الخصم:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.discount_label = ttk.Label(amounts_info_frame, text="-", foreground="red")
        self.discount_label.grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)
        
        # المبلغ النهائي
        ttk.Label(amounts_info_frame, text="المبلغ النهائي:", font=FONTS['heading']).grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.final_amount_detail_label = ttk.Label(amounts_info_frame, text="-", font=FONTS['heading'], foreground="green")
        self.final_amount_detail_label.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # الملاحظات
        ttk.Label(details_frame, text="الملاحظات:", font=FONTS['default']).pack(anchor=tk.W, pady=(10, 0))
        self.notes_text = tk.Text(details_frame, height=2, state=tk.DISABLED)
        self.notes_text.pack(fill=tk.X, pady=5)
    
    def setup_treeview(self, treeview, columns):
        """إعداد جدول البيانات"""
        treeview['columns'] = list(columns.keys())
        treeview['show'] = 'headings'
        
        for col_id, col_info in columns.items():
            treeview.heading(col_id, text=col_info['text'])
            treeview.column(col_id, width=col_info['width'], anchor='center')
    
    def refresh_invoices(self):
        """تحديث قائمة الفواتير"""
        try:
            # مسح البيانات الحالية
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)
            
            # إضافة بيانات تجريبية
            sample_invoices = [
                (1, "أحمد محمد", "2024-01-15", "50.00", "25.00", "75.00", "5.00", "70.00", "نقدي"),
                (2, "فاطمة علي", "2024-01-15", "30.00", "15.00", "45.00", "0.00", "45.00", "بطاقة"),
                (3, "محمد أحمد", "2024-01-14", "40.00", "20.00", "60.00", "10.00", "50.00", "نقدي")
            ]
            
            for invoice in sample_invoices:
                self.invoices_tree.insert('', 'end', values=invoice)
                
        except Exception as e:
            show_error(f"خطأ في تحديث قائمة الفواتير: {e}")
    
    def search_invoices(self):
        """البحث في الفواتير"""
        # هنا يمكن إضافة منطق البحث الفعلي
        self.refresh_invoices()
    
    def on_invoice_select(self, event=None):
        """عند اختيار فاتورة"""
        selection = self.invoices_tree.selection()
        if selection:
            item = self.invoices_tree.item(selection[0])
            values = item['values']
            
            # تحديث تفاصيل الفاتورة
            self.invoice_id_label.config(text=f"#{values[0]}")
            self.customer_label.config(text=values[1])
            self.date_label.config(text=values[2])
            self.payment_method_label.config(text=values[8])
            self.final_amount_label.config(text=f"{values[7]} ريال")
            
            # تحديث تفاصيل المبالغ
            self.session_amount_label.config(text=f"{values[3]} ريال")
            self.purchases_amount_label.config(text=f"{values[4]} ريال")
            self.total_amount_label.config(text=f"{values[5]} ريال")
            self.discount_label.config(text=f"{values[6]} ريال")
            self.final_amount_detail_label.config(text=f"{values[7]} ريال")
            
            # تحديث الملاحظات
            self.notes_text.config(state=tk.NORMAL)
            self.notes_text.delete(1.0, tk.END)
            self.notes_text.insert(1.0, "لا توجد ملاحظات")
            self.notes_text.config(state=tk.DISABLED)
    
    def create_new_invoice(self):
        """إنشاء فاتورة جديدة"""
        show_warning("سيتم تطوير هذه الميزة قريباً")
    
    def print_invoice(self):
        """طباعة الفاتورة المحددة"""
        selection = self.invoices_tree.selection()
        if not selection:
            show_warning("يرجى اختيار فاتورة للطباعة")
            return
        
        show_warning("سيتم تطوير ميزة الطباعة قريباً")
