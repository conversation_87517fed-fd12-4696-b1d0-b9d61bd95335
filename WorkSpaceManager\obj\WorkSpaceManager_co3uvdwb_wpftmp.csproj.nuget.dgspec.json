{"format": 1, "restore": {"D:\\sokoun\\WorkSpaceManager\\WorkSpaceManager.csproj": {}}, "projects": {"D:\\sokoun\\WorkSpaceManager\\WorkSpaceManager.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\sokoun\\WorkSpaceManager\\WorkSpaceManager.csproj", "projectName": "WorkSpaceManager", "projectPath": "D:\\sokoun\\WorkSpaceManager\\WorkSpaceManager.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\sokoun\\WorkSpaceManager\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Data.Sqlite": {"target": "Package", "version": "[6.0.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}