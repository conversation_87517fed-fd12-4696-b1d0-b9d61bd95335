"""
النافذة الرئيسية لنظام إدارة مساحة العمل
Main Window for Work Space Manager
"""

import tkinter as tk
from tkinter import ttk, messagebox
from database.database_manager import DatabaseManager
from utils.config import WINDOW_TITLE, WINDOW_SIZE, COLORS, FONTS
from utils.helpers import center_window, show_error
from .dashboard import DashboardFrame
from .customers import CustomersFrame
from .products import ProductsFrame
from .sessions import SessionsFrame
from .invoices import InvoicesFrame
from .reports import ReportsFrame

class MainWindow:
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.db = DatabaseManager()
        self.current_frame = None
        self.setup_window()
        self.create_menu()
        self.create_main_layout()
        self.show_dashboard()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(WINDOW_TITLE)
        self.root.geometry(WINDOW_SIZE)
        self.root.configure(bg=COLORS['background'])
        
        # توسيط النافذة
        center_window(self.root, 1200, 800)
        
        # منع تغيير حجم النافذة لأقل من الحد الأدنى
        self.root.minsize(800, 600)
        
        # إعداد الأيقونة (إذا كانت متوفرة)
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
    
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="نسخة احتياطية", command=self.backup_database)
        file_menu.add_command(label="استعادة", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.on_closing)
        
        # قائمة العرض
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="عرض", menu=view_menu)
        view_menu.add_command(label="لوحة التحكم", command=self.show_dashboard)
        view_menu.add_command(label="العملاء", command=self.show_customers)
        view_menu.add_command(label="المنتجات", command=self.show_products)
        view_menu.add_command(label="الجلسات", command=self.show_sessions)
        view_menu.add_command(label="الفواتير", command=self.show_invoices)
        view_menu.add_command(label="التقارير", command=self.show_reports)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_main_layout(self):
        """إنشاء التخطيط الرئيسي"""
        # إطار علوي للعنوان والأزرار
        self.header_frame = ttk.Frame(self.root)
        self.header_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # عنوان التطبيق
        title_label = ttk.Label(
            self.header_frame, 
            text="نظام إدارة مساحة العمل", 
            font=FONTS['large']
        )
        title_label.pack(side=tk.LEFT)
        
        # أزرار التنقل السريع
        self.create_navigation_buttons()
        
        # إطار رئيسي للمحتوى
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # شريط الحالة
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.status_label = ttk.Label(
            self.status_frame, 
            text="جاهز", 
            font=FONTS['small']
        )
        self.status_label.pack(side=tk.LEFT)
        
        # معلومات الوقت
        self.time_label = ttk.Label(
            self.status_frame, 
            text="", 
            font=FONTS['small']
        )
        self.time_label.pack(side=tk.RIGHT)
        
        # تحديث الوقت
        self.update_time()
    
    def create_navigation_buttons(self):
        """إنشاء أزرار التنقل"""
        button_frame = ttk.Frame(self.header_frame)
        button_frame.pack(side=tk.RIGHT)
        
        buttons = [
            ("🏠 الرئيسية", self.show_dashboard),
            ("👥 العملاء", self.show_customers),
            ("🛍️ المنتجات", self.show_products),
            ("⏰ الجلسات", self.show_sessions),
            ("🧾 الفواتير", self.show_invoices),
            ("📋 التقارير", self.show_reports)
        ]
        
        for text, command in buttons:
            btn = ttk.Button(
                button_frame, 
                text=text, 
                command=command,
                width=12
            )
            btn.pack(side=tk.LEFT, padx=2)
    
    def clear_main_frame(self):
        """مسح المحتوى الرئيسي"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        self.current_frame = None
    
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_main_frame()
        self.current_frame = DashboardFrame(self.main_frame, self.db)
        self.update_status("لوحة التحكم")
    
    def show_customers(self):
        """عرض إدارة العملاء"""
        self.clear_main_frame()
        self.current_frame = CustomersFrame(self.main_frame, self.db)
        self.update_status("إدارة العملاء")
    
    def show_products(self):
        """عرض إدارة المنتجات"""
        self.clear_main_frame()
        self.current_frame = ProductsFrame(self.main_frame, self.db)
        self.update_status("إدارة المنتجات")
    
    def show_sessions(self):
        """عرض إدارة الجلسات"""
        self.clear_main_frame()
        self.current_frame = SessionsFrame(self.main_frame, self.db)
        self.update_status("إدارة الجلسات")
    
    def show_invoices(self):
        """عرض إدارة الفواتير"""
        self.clear_main_frame()
        self.current_frame = InvoicesFrame(self.main_frame, self.db)
        self.update_status("إدارة الفواتير")

    def show_reports(self):
        """عرض التقارير والجرد"""
        self.clear_main_frame()
        self.current_frame = ReportsFrame(self.main_frame, self.db)
        self.update_status("التقارير والجرد")
    
    def update_status(self, message: str):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
    
    def update_time(self):
        """تحديث عرض الوقت"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)  # تحديث كل ثانية
    
    def backup_database(self):
        """نسخة احتياطية من قاعدة البيانات"""
        try:
            from datetime import datetime
            import shutil
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"data/backup_workspace_{timestamp}.db"
            
            shutil.copy2(self.db.db_path, backup_path)
            messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية:\n{backup_path}")
        except Exception as e:
            show_error(f"خطأ في إنشاء النسخة الاحتياطية:\n{str(e)}")
    
    def restore_database(self):
        """استعادة قاعدة البيانات"""
        from tkinter import filedialog
        
        file_path = filedialog.askopenfilename(
            title="اختر ملف قاعدة البيانات",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                import shutil
                shutil.copy2(file_path, self.db.db_path)
                messagebox.showinfo("نجح", "تم استعادة قاعدة البيانات بنجاح")
                # إعادة تحميل البيانات
                if self.current_frame and hasattr(self.current_frame, 'refresh'):
                    self.current_frame.refresh()
            except Exception as e:
                show_error(f"خطأ في استعادة قاعدة البيانات:\n{str(e)}")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
نظام إدارة مساحة العمل
Work Space Manager

الإصدار: 1.0
تطوير: Python + Tkinter

المميزات:
• إدارة العملاء والمنتجات
• تتبع الجلسات والوقت
• نظام فواتير متكامل
• تقارير وإحصائيات
• نسخ احتياطي آمن

© 2024 جميع الحقوق محفوظة
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def on_closing(self):
        """عند إغلاق التطبيق"""
        if messagebox.askokcancel("خروج", "هل أنت متأكد من إغلاق التطبيق؟"):
            self.root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
