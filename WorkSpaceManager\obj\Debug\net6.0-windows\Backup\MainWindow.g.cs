﻿#pragma checksum "..\..\..\..\Backup\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DFD23CB53968AB50F6607A618DD6B927E68616C9"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WorkSpaceManager.Views {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 29 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtCurrentShift;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnThemeToggle;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDashboard;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCustomers;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSessions;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnProducts;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnInvoices;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnShifts;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnReports;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSettings;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Frame MainFrame;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Backup\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtShiftTime;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WorkSpaceManager;component/backup/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Backup\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtCurrentShift = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.BtnThemeToggle = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\..\..\Backup\MainWindow.xaml"
            this.BtnThemeToggle.Click += new System.Windows.RoutedEventHandler(this.BtnThemeToggle_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnDashboard = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\..\Backup\MainWindow.xaml"
            this.BtnDashboard.Click += new System.Windows.RoutedEventHandler(this.BtnDashboard_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnCustomers = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\Backup\MainWindow.xaml"
            this.BtnCustomers.Click += new System.Windows.RoutedEventHandler(this.BtnCustomers_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnSessions = ((System.Windows.Controls.Button)(target));
            
            #line 56 "..\..\..\..\Backup\MainWindow.xaml"
            this.BtnSessions.Click += new System.Windows.RoutedEventHandler(this.BtnSessions_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnProducts = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\..\Backup\MainWindow.xaml"
            this.BtnProducts.Click += new System.Windows.RoutedEventHandler(this.BtnProducts_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnInvoices = ((System.Windows.Controls.Button)(target));
            
            #line 62 "..\..\..\..\Backup\MainWindow.xaml"
            this.BtnInvoices.Click += new System.Windows.RoutedEventHandler(this.BtnInvoices_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnShifts = ((System.Windows.Controls.Button)(target));
            
            #line 65 "..\..\..\..\Backup\MainWindow.xaml"
            this.BtnShifts.Click += new System.Windows.RoutedEventHandler(this.BtnShifts_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnReports = ((System.Windows.Controls.Button)(target));
            
            #line 68 "..\..\..\..\Backup\MainWindow.xaml"
            this.BtnReports.Click += new System.Windows.RoutedEventHandler(this.BtnReports_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnSettings = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\..\Backup\MainWindow.xaml"
            this.BtnSettings.Click += new System.Windows.RoutedEventHandler(this.BtnSettings_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.MainFrame = ((System.Windows.Controls.Frame)(target));
            return;
            case 12:
            this.TxtShiftTime = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

