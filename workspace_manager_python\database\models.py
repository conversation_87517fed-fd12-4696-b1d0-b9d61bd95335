"""
نماذج البيانات لنظام إدارة مساحة العمل
Data Models for Work Space Manager
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional

@dataclass
class Customer:
    """نموذج العميل"""
    id: Optional[int] = None
    name: str = ""
    phone: str = ""
    email: str = ""
    registration_date: datetime = None
    is_active: bool = True
    notes: str = ""
    
    def __post_init__(self):
        if self.registration_date is None:
            self.registration_date = datetime.now()

@dataclass
class Product:
    """نموذج المنتج"""
    id: Optional[int] = None
    name: str = ""
    category: str = ""
    price: float = 0.0
    cost: float = 0.0
    quantity: int = 0
    barcode: str = ""
    is_active: bool = True
    description: str = ""
    
    @property
    def profit(self) -> float:
        """حساب الربح"""
        return self.price - self.cost

@dataclass
class Session:
    """نموذج الجلسة"""
    id: Optional[int] = None
    customer_id: int = 0
    customer_name: str = ""
    start_time: datetime = None
    end_time: Optional[datetime] = None
    hourly_rate: float = 0.0
    total_hours: float = 0.0
    total_amount: float = 0.0
    is_active: bool = True
    notes: str = ""
    
    def __post_init__(self):
        if self.start_time is None:
            self.start_time = datetime.now()
    
    @property
    def duration_minutes(self) -> int:
        """مدة الجلسة بالدقائق"""
        if self.end_time and self.start_time:
            delta = self.end_time - self.start_time
            return int(delta.total_seconds() / 60)
        return 0
    
    def calculate_total(self):
        """حساب إجمالي التكلفة"""
        if self.end_time and self.start_time:
            hours = (self.end_time - self.start_time).total_seconds() / 3600
            self.total_hours = round(hours, 2)
            self.total_amount = round(self.total_hours * self.hourly_rate, 2)

@dataclass
class Purchase:
    """نموذج المشتريات"""
    id: Optional[int] = None
    session_id: int = 0
    product_id: int = 0
    product_name: str = ""
    quantity: int = 1
    unit_price: float = 0.0
    total_price: float = 0.0
    purchase_time: datetime = None
    notes: str = ""
    
    def __post_init__(self):
        if self.purchase_time is None:
            self.purchase_time = datetime.now()
        self.calculate_total()
    
    def calculate_total(self):
        """حساب إجمالي السعر"""
        self.total_price = self.quantity * self.unit_price

@dataclass
class Invoice:
    """نموذج الفاتورة"""
    id: Optional[int] = None
    session_id: int = 0
    customer_id: int = 0
    customer_name: str = ""
    invoice_date: datetime = None
    session_amount: float = 0.0
    purchases_amount: float = 0.0
    total_amount: float = 0.0
    discount: float = 0.0
    discount_type: str = "مبلغ"  # "مبلغ" أو "نسبة"
    final_amount: float = 0.0
    paid_amount: float = 0.0
    remaining_amount: float = 0.0
    payment_method: str = "نقدي"
    payment_status: str = "مدفوع جزئياً"  # "مدفوع", "مدفوع جزئياً", "غير مدفوع"
    notes: str = ""

    def __post_init__(self):
        if self.invoice_date is None:
            self.invoice_date = datetime.now()
        self.calculate_totals()

    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        self.total_amount = self.session_amount + self.purchases_amount

        # حساب الخصم
        if self.discount_type == "نسبة":
            discount_amount = self.total_amount * (self.discount / 100)
        else:
            discount_amount = self.discount

        self.final_amount = self.total_amount - discount_amount
        self.remaining_amount = self.final_amount - self.paid_amount

        # تحديث حالة الدفع
        if self.paid_amount >= self.final_amount:
            self.payment_status = "مدفوع"
            self.remaining_amount = 0
        elif self.paid_amount > 0:
            self.payment_status = "مدفوع جزئياً"
        else:
            self.payment_status = "غير مدفوع"

@dataclass
class Shift:
    """نموذج الشيفت"""
    id: Optional[int] = None
    start_time: datetime = None
    end_time: Optional[datetime] = None
    opening_cash: float = 0.0
    closing_cash: float = 0.0
    total_sales: float = 0.0
    total_sessions: int = 0
    notes: str = ""
    is_active: bool = True
    
    def __post_init__(self):
        if self.start_time is None:
            self.start_time = datetime.now()
