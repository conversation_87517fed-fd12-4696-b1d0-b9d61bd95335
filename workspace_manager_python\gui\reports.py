"""
واجهة التقارير والجرد
Reports and Inventory Interface
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime, timedelta
from utils.config import FONTS
from utils.helpers import (
    show_success, show_error, show_warning,
    format_currency, format_datetime, get_current_date_string
)

class ReportsFrame:
    """إطار التقارير والجرد"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        self.create_interface()
        self.refresh_reports()
    
    def create_interface(self):
        """إنشاء واجهة التقارير"""
        # عنوان الصفحة
        title_label = ttk.Label(
            self.frame, 
            text="📊 التقارير والجرد", 
            font=FONTS['title']
        )
        title_label.pack(pady=10)
        
        # إطار فلاتر التقارير
        self.create_filters_section()
        
        # إطار الإحصائيات السريعة
        self.create_quick_stats_section()
        
        # إطار التقارير التفصيلية
        self.create_detailed_reports_section()
    
    def create_filters_section(self):
        """إنشاء قسم الفلاتر"""
        filters_frame = ttk.LabelFrame(self.frame, text="🔍 فلاتر التقارير", padding=10)
        filters_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # الصف الأول - فلاتر التاريخ
        date_frame = ttk.Frame(filters_frame)
        date_frame.pack(fill=tk.X, pady=5)
        
        # نوع التقرير
        ttk.Label(date_frame, text="نوع التقرير:").pack(side=tk.LEFT, padx=5)
        self.report_type_var = tk.StringVar(value="يومي")
        self.report_type_combo = ttk.Combobox(date_frame, textvariable=self.report_type_var,
                                            values=["يومي", "أسبوعي", "شهري", "مخصص"], 
                                            state="readonly", width=10)
        self.report_type_combo.pack(side=tk.LEFT, padx=5)
        self.report_type_combo.bind('<<ComboboxSelected>>', self.on_report_type_change)
        
        # من تاريخ
        ttk.Label(date_frame, text="من تاريخ:").pack(side=tk.LEFT, padx=(20, 5))
        self.from_date_var = tk.StringVar(value=get_current_date_string())
        self.from_date_entry = ttk.Entry(date_frame, textvariable=self.from_date_var, width=12)
        self.from_date_entry.pack(side=tk.LEFT, padx=5)
        
        # إلى تاريخ
        ttk.Label(date_frame, text="إلى تاريخ:").pack(side=tk.LEFT, padx=5)
        self.to_date_var = tk.StringVar(value=get_current_date_string())
        self.to_date_entry = ttk.Entry(date_frame, textvariable=self.to_date_var, width=12)
        self.to_date_entry.pack(side=tk.LEFT, padx=5)
        
        # أزرار الإجراءات
        buttons_frame = ttk.Frame(date_frame)
        buttons_frame.pack(side=tk.RIGHT, padx=10)
        
        ttk.Button(
            buttons_frame, 
            text="📊 إنشاء التقرير", 
            command=self.generate_report
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="📄 تصدير", 
            command=self.export_report
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="🔄 تحديث", 
            command=self.refresh_reports
        ).pack(side=tk.LEFT, padx=2)
    
    def create_quick_stats_section(self):
        """إنشاء قسم الإحصائيات السريعة"""
        stats_frame = ttk.LabelFrame(self.frame, text="📈 الإحصائيات السريعة", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # إطار البطاقات
        cards_frame = ttk.Frame(stats_frame)
        cards_frame.pack(fill=tk.X)
        
        # بطاقة إجمالي المبيعات
        self.total_sales_card = self.create_stat_card(
            cards_frame, "💰 إجمالي المبيعات", "0.00 جنيه", "#4CAF50"
        )
        self.total_sales_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة عدد الجلسات
        self.sessions_count_card = self.create_stat_card(
            cards_frame, "⏰ عدد الجلسات", "0", "#2196F3"
        )
        self.sessions_count_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة عدد العملاء
        self.customers_count_card = self.create_stat_card(
            cards_frame, "👥 عدد العملاء", "0", "#FF9800"
        )
        self.customers_count_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        # بطاقة متوسط الجلسة
        self.avg_session_card = self.create_stat_card(
            cards_frame, "📊 متوسط الجلسة", "0.00 جنيه", "#9C27B0"
        )
        self.avg_session_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
    
    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk.Frame(parent, relief=tk.RAISED, borderwidth=1)
        
        # عنوان البطاقة
        title_label = ttk.Label(
            card_frame, 
            text=title, 
            font=FONTS['default'],
            foreground=color
        )
        title_label.pack(pady=5)
        
        # قيمة البطاقة
        value_label = ttk.Label(
            card_frame, 
            text=value, 
            font=FONTS['heading']
        )
        value_label.pack(pady=5)
        
        # حفظ مرجع للتحديث لاحقاً
        card_frame.value_label = value_label
        
        return card_frame
    
    def create_detailed_reports_section(self):
        """إنشاء قسم التقارير التفصيلية"""
        reports_frame = ttk.LabelFrame(self.frame, text="📋 التقارير التفصيلية", padding=10)
        reports_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # دفتر التبويبات للتقارير المختلفة
        self.reports_notebook = ttk.Notebook(reports_frame)
        self.reports_notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب تقرير المبيعات
        self.create_sales_report_tab()
        
        # تبويب تقرير الجلسات
        self.create_sessions_report_tab()
        
        # تبويب تقرير العملاء
        self.create_customers_report_tab()
        
        # تبويب تقرير المنتجات
        self.create_products_report_tab()
    
    def create_sales_report_tab(self):
        """إنشاء تبويب تقرير المبيعات"""
        sales_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(sales_frame, text="💰 المبيعات")
        
        # جدول المبيعات
        columns = {
            'date': {'text': 'التاريخ', 'width': 100},
            'sessions_count': {'text': 'عدد الجلسات', 'width': 100},
            'sessions_revenue': {'text': 'إيرادات الجلسات', 'width': 120},
            'products_revenue': {'text': 'إيرادات المنتجات', 'width': 120},
            'total_revenue': {'text': 'إجمالي الإيرادات', 'width': 120},
            'customers_count': {'text': 'عدد العملاء', 'width': 100}
        }
        
        self.sales_tree = ttk.Treeview(sales_frame)
        self.setup_treeview(self.sales_tree, columns)
        
        # شريط التمرير
        sales_scrollbar = ttk.Scrollbar(sales_frame, orient=tk.VERTICAL, command=self.sales_tree.yview)
        self.sales_tree.configure(yscrollcommand=sales_scrollbar.set)
        
        self.sales_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sales_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_sessions_report_tab(self):
        """إنشاء تبويب تقرير الجلسات"""
        sessions_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(sessions_frame, text="⏰ الجلسات")
        
        # جدول الجلسات
        columns = {
            'id': {'text': 'رقم الجلسة', 'width': 80},
            'customer': {'text': 'العميل', 'width': 150},
            'start_time': {'text': 'وقت البداية', 'width': 120},
            'end_time': {'text': 'وقت النهاية', 'width': 120},
            'duration': {'text': 'المدة', 'width': 100},
            'amount': {'text': 'المبلغ', 'width': 100}
        }
        
        self.sessions_tree = ttk.Treeview(sessions_frame)
        self.setup_treeview(self.sessions_tree, columns)
        
        # شريط التمرير
        sessions_scrollbar = ttk.Scrollbar(sessions_frame, orient=tk.VERTICAL, command=self.sessions_tree.yview)
        self.sessions_tree.configure(yscrollcommand=sessions_scrollbar.set)
        
        self.sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sessions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_customers_report_tab(self):
        """إنشاء تبويب تقرير العملاء"""
        customers_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(customers_frame, text="👥 العملاء")
        
        # جدول العملاء
        columns = {
            'name': {'text': 'اسم العميل', 'width': 150},
            'sessions_count': {'text': 'عدد الجلسات', 'width': 100},
            'total_hours': {'text': 'إجمالي الساعات', 'width': 100},
            'total_spent': {'text': 'إجمالي المبلغ', 'width': 120},
            'avg_session': {'text': 'متوسط الجلسة', 'width': 100},
            'last_visit': {'text': 'آخر زيارة', 'width': 120}
        }
        
        self.customers_tree = ttk.Treeview(customers_frame)
        self.setup_treeview(self.customers_tree, columns)
        
        # شريط التمرير
        customers_scrollbar = ttk.Scrollbar(customers_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=customers_scrollbar.set)
        
        self.customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        customers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_products_report_tab(self):
        """إنشاء تبويب تقرير المنتجات"""
        products_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(products_frame, text="🛍️ المنتجات")
        
        # جدول المنتجات
        columns = {
            'name': {'text': 'اسم المنتج', 'width': 150},
            'category': {'text': 'الفئة', 'width': 100},
            'sold_quantity': {'text': 'الكمية المباعة', 'width': 100},
            'revenue': {'text': 'الإيرادات', 'width': 100},
            'profit': {'text': 'الربح', 'width': 100},
            'remaining_stock': {'text': 'المخزون المتبقي', 'width': 120}
        }
        
        self.products_tree = ttk.Treeview(products_frame)
        self.setup_treeview(self.products_tree, columns)
        
        # شريط التمرير
        products_scrollbar = ttk.Scrollbar(products_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        products_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_treeview(self, treeview, columns):
        """إعداد جدول البيانات"""
        treeview['columns'] = list(columns.keys())
        treeview['show'] = 'headings'
        
        for col_id, col_info in columns.items():
            treeview.heading(col_id, text=col_info['text'])
            treeview.column(col_id, width=col_info['width'], anchor='center')
    
    def on_report_type_change(self, event=None):
        """عند تغيير نوع التقرير"""
        report_type = self.report_type_var.get()
        today = datetime.now()
        
        if report_type == "يومي":
            self.from_date_var.set(today.strftime("%Y-%m-%d"))
            self.to_date_var.set(today.strftime("%Y-%m-%d"))
        elif report_type == "أسبوعي":
            week_start = today - timedelta(days=today.weekday())
            week_end = week_start + timedelta(days=6)
            self.from_date_var.set(week_start.strftime("%Y-%m-%d"))
            self.to_date_var.set(week_end.strftime("%Y-%m-%d"))
        elif report_type == "شهري":
            month_start = today.replace(day=1)
            next_month = month_start.replace(month=month_start.month + 1) if month_start.month < 12 else month_start.replace(year=month_start.year + 1, month=1)
            month_end = next_month - timedelta(days=1)
            self.from_date_var.set(month_start.strftime("%Y-%m-%d"))
            self.to_date_var.set(month_end.strftime("%Y-%m-%d"))
    
    def generate_report(self):
        """إنشاء التقرير"""
        try:
            from_date = self.from_date_var.get()
            to_date = self.to_date_var.get()
            
            # تحديث الإحصائيات السريعة
            self.update_quick_stats(from_date, to_date)
            
            # تحديث التقارير التفصيلية
            self.update_detailed_reports(from_date, to_date)
            
            show_success("تم إنشاء التقرير بنجاح")
            
        except Exception as e:
            show_error(f"خطأ في إنشاء التقرير: {e}")
    
    def update_quick_stats(self, from_date, to_date):
        """تحديث الإحصائيات السريعة"""
        try:
            # هنا يمكن إضافة منطق حساب الإحصائيات من قاعدة البيانات
            # حالياً سنعرض بيانات تجريبية
            
            self.total_sales_card.value_label.config(text="1,250.00 جنيه")
            self.sessions_count_card.value_label.config(text="25")
            self.customers_count_card.value_label.config(text="15")
            self.avg_session_card.value_label.config(text="50.00 جنيه")
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    def update_detailed_reports(self, from_date, to_date):
        """تحديث التقارير التفصيلية"""
        try:
            # مسح البيانات الحالية
            for tree in [self.sales_tree, self.sessions_tree, self.customers_tree, self.products_tree]:
                for item in tree.get_children():
                    tree.delete(item)
            
            # إضافة بيانات تجريبية
            self.add_sample_data()
            
        except Exception as e:
            print(f"خطأ في تحديث التقارير التفصيلية: {e}")
    
    def add_sample_data(self):
        """إضافة بيانات تجريبية"""
        # بيانات المبيعات
        sales_data = [
            ("2024-01-15", "10", "500.00", "150.00", "650.00", "8"),
            ("2024-01-14", "8", "400.00", "120.00", "520.00", "6"),
            ("2024-01-13", "12", "600.00", "180.00", "780.00", "10")
        ]
        
        for data in sales_data:
            self.sales_tree.insert('', 'end', values=data)
        
        # بيانات الجلسات
        sessions_data = [
            ("001", "أحمد محمد", "2024-01-15 10:00", "2024-01-15 13:00", "3 ساعات", "45.00"),
            ("002", "فاطمة علي", "2024-01-15 14:00", "2024-01-15 16:30", "2.5 ساعة", "37.50"),
            ("003", "محمد أحمد", "2024-01-15 17:00", "2024-01-15 20:00", "3 ساعات", "45.00")
        ]
        
        for data in sessions_data:
            self.sessions_tree.insert('', 'end', values=data)
        
        # بيانات العملاء
        customers_data = [
            ("أحمد محمد", "5", "15 ساعة", "225.00", "45.00", "2024-01-15"),
            ("فاطمة علي", "3", "8 ساعات", "120.00", "40.00", "2024-01-14"),
            ("محمد أحمد", "4", "12 ساعة", "180.00", "45.00", "2024-01-15")
        ]
        
        for data in customers_data:
            self.customers_tree.insert('', 'end', values=data)
        
        # بيانات المنتجات
        products_data = [
            ("شاي", "مشروبات ساخنة", "25", "125.00", "75.00", "25"),
            ("قهوة", "مشروبات ساخنة", "15", "120.00", "75.00", "15"),
            ("عصير برتقال", "مشروبات باردة", "10", "120.00", "70.00", "15")
        ]
        
        for data in products_data:
            self.products_tree.insert('', 'end', values=data)
    
    def refresh_reports(self):
        """تحديث التقارير"""
        self.generate_report()
    
    def export_report(self):
        """تصدير التقرير"""
        show_warning("سيتم تطوير ميزة التصدير قريباً")
