﻿#pragma checksum "..\..\..\..\Views\AddProductToSessionWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6C0766FC15259BD80C892BAEA1400B35D675E728"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WorkSpaceManager.Views {
    
    
    /// <summary>
    /// AddProductToSessionWindow
    /// </summary>
    public partial class AddProductToSessionWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtProductName;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtProductCategory;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtProductPrice;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtProductQuantity;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbSessions;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtQuantity;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtTotalPrice;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SessionInfo;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAdd;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WorkSpaceManager;component/views/addproducttosessionwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtProductName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtProductCategory = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TxtProductPrice = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TxtProductQuantity = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.CmbSessions = ((System.Windows.Controls.ComboBox)(target));
            
            #line 76 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
            this.CmbSessions.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbSessions_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 103 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDecrease_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TxtQuantity = ((System.Windows.Controls.TextBox)(target));
            
            #line 114 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
            this.TxtQuantity.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtQuantity_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 122 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnIncrease_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.TxtTotalPrice = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.SessionInfo = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.TxtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.BtnAdd = ((System.Windows.Controls.Button)(target));
            
            #line 179 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
            this.BtnAdd.Click += new System.Windows.RoutedEventHandler(this.BtnAdd_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 186 "..\..\..\..\Views\AddProductToSessionWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

