"""
واجهة إدارة الجلسات
Sessions Management Interface
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from database.models import Session
from utils.config import FONTS, DEFAULT_HOURLY_RATE
from utils.helpers import (
    show_success, show_error, show_warning, ask_confirmation,
    format_currency, format_datetime, calculate_time_difference
)

class SessionsFrame:
    """إطار إدارة الجلسات"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        self.create_interface()
        self.refresh_sessions()
        self.update_timer()
    
    def create_interface(self):
        """إنشاء واجهة إدارة الجلسات"""
        # عنوان الصفحة
        title_label = ttk.Label(
            self.frame, 
            text="⏰ إدارة الجلسات", 
            font=FONTS['title']
        )
        title_label.pack(pady=10)
        
        # إطار بدء جلسة جديدة
        self.create_new_session_section()
        
        # إطار الجلسات النشطة
        self.create_active_sessions_section()
        
        # إطار تاريخ الجلسات
        self.create_sessions_history_section()
    
    def create_new_session_section(self):
        """إنشاء قسم بدء جلسة جديدة"""
        new_session_frame = ttk.LabelFrame(self.frame, text="🆕 بدء جلسة جديدة", padding=10)
        new_session_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # حقول بدء الجلسة
        fields_frame = ttk.Frame(new_session_frame)
        fields_frame.pack(fill=tk.X)
        
        # اختيار العميل
        ttk.Label(fields_frame, text="العميل:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.customer_var = tk.StringVar()
        self.customer_combo = ttk.Combobox(fields_frame, textvariable=self.customer_var, width=25, state="readonly")
        self.customer_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # سعر الساعة
        ttk.Label(fields_frame, text="سعر الساعة:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.hourly_rate_var = tk.StringVar(value=str(DEFAULT_HOURLY_RATE))
        self.hourly_rate_entry = ttk.Entry(fields_frame, textvariable=self.hourly_rate_var, width=10)
        self.hourly_rate_entry.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # زر بدء الجلسة
        ttk.Button(
            fields_frame, 
            text="▶️ بدء الجلسة", 
            command=self.start_new_session
        ).grid(row=0, column=4, padx=20, pady=5)
        
        # تحديث قائمة العملاء
        self.refresh_customers_list()
    
    def create_active_sessions_section(self):
        """إنشاء قسم الجلسات النشطة"""
        active_frame = ttk.LabelFrame(self.frame, text="🔴 الجلسات النشطة", padding=10)
        active_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # جدول الجلسات النشطة
        columns = {
            'id': {'text': 'رقم الجلسة', 'width': 80},
            'customer': {'text': 'العميل', 'width': 150},
            'start_time': {'text': 'وقت البداية', 'width': 120},
            'duration': {'text': 'المدة', 'width': 100},
            'rate': {'text': 'سعر الساعة', 'width': 80},
            'current_cost': {'text': 'التكلفة الحالية', 'width': 100}
        }
        
        self.active_sessions_tree = ttk.Treeview(active_frame)
        self.setup_treeview(self.active_sessions_tree, columns)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(active_frame, orient=tk.VERTICAL, command=self.active_sessions_tree.yview)
        self.active_sessions_tree.configure(yscrollcommand=scrollbar.set)
        
        self.active_sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار الإجراءات
        actions_frame = ttk.Frame(active_frame)
        actions_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(
            actions_frame, 
            text="⏹️ إنهاء الجلسة", 
            command=self.end_session
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            actions_frame, 
            text="🛍️ إضافة مشترى", 
            command=self.add_purchase
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            actions_frame, 
            text="🧾 إنشاء فاتورة", 
            command=self.create_invoice
        ).pack(side=tk.LEFT, padx=5)
    
    def create_sessions_history_section(self):
        """إنشاء قسم تاريخ الجلسات"""
        history_frame = ttk.LabelFrame(self.frame, text="📋 تاريخ الجلسات", padding=10)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # فلتر التاريخ
        filter_frame = ttk.Frame(history_frame)
        filter_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(filter_frame, text="من تاريخ:").pack(side=tk.LEFT, padx=5)
        self.from_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.from_date_entry = ttk.Entry(filter_frame, textvariable=self.from_date_var, width=12)
        self.from_date_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(filter_frame, text="إلى تاريخ:").pack(side=tk.LEFT, padx=5)
        self.to_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.to_date_entry = ttk.Entry(filter_frame, textvariable=self.to_date_var, width=12)
        self.to_date_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            filter_frame, 
            text="🔍 بحث", 
            command=self.search_sessions
        ).pack(side=tk.LEFT, padx=10)
        
        # جدول تاريخ الجلسات
        history_columns = {
            'id': {'text': 'رقم الجلسة', 'width': 80},
            'customer': {'text': 'العميل', 'width': 150},
            'start_time': {'text': 'وقت البداية', 'width': 120},
            'end_time': {'text': 'وقت النهاية', 'width': 120},
            'duration': {'text': 'المدة', 'width': 100},
            'total_amount': {'text': 'إجمالي التكلفة', 'width': 100}
        }
        
        self.history_tree = ttk.Treeview(history_frame)
        self.setup_treeview(self.history_tree, history_columns)
        
        # شريط التمرير للتاريخ
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_treeview(self, treeview, columns):
        """إعداد جدول البيانات"""
        treeview['columns'] = list(columns.keys())
        treeview['show'] = 'headings'
        
        for col_id, col_info in columns.items():
            treeview.heading(col_id, text=col_info['text'])
            treeview.column(col_id, width=col_info['width'], anchor='center')
    
    def refresh_customers_list(self):
        """تحديث قائمة العملاء"""
        try:
            customers = self.db.get_customers()
            customer_names = [f"{c.name} (ID: {c.id})" for c in customers if c.is_active]
            self.customer_combo['values'] = customer_names
        except Exception as e:
            show_error(f"خطأ في تحميل قائمة العملاء: {e}")
    
    def start_new_session(self):
        """بدء جلسة جديدة"""
        try:
            # التحقق من البيانات
            customer_text = self.customer_var.get()
            if not customer_text:
                show_warning("يرجى اختيار عميل")
                return
            
            # استخراج معرف العميل
            customer_id = int(customer_text.split("ID: ")[1].split(")")[0])
            customer_name = customer_text.split(" (ID:")[0]
            
            hourly_rate = float(self.hourly_rate_var.get())
            if hourly_rate <= 0:
                show_warning("يرجى إدخال سعر ساعة صحيح")
                return
            
            # التحقق من عدم وجود جلسة نشطة للعميل
            active_sessions = self.db.get_active_sessions()
            if any(s.customer_id == customer_id for s in active_sessions):
                show_warning("يوجد جلسة نشطة لهذا العميل بالفعل")
                return
            
            # بدء الجلسة
            session_id = self.db.start_session(customer_id, customer_name, hourly_rate)
            show_success(f"تم بدء الجلسة رقم {session_id} بنجاح")
            
            # تحديث الواجهة
            self.refresh_sessions()
            
        except ValueError:
            show_error("يرجى إدخال سعر ساعة صحيح")
        except Exception as e:
            show_error(f"خطأ في بدء الجلسة: {e}")
    
    def refresh_sessions(self):
        """تحديث الجلسات"""
        self.refresh_active_sessions()
        self.search_sessions()
    
    def refresh_active_sessions(self):
        """تحديث الجلسات النشطة"""
        try:
            # مسح البيانات الحالية
            for item in self.active_sessions_tree.get_children():
                self.active_sessions_tree.delete(item)
            
            # الحصول على الجلسات النشطة
            active_sessions = self.db.get_active_sessions()
            
            for session in active_sessions:
                # حساب المدة الحالية
                time_diff = calculate_time_difference(session.start_time)
                duration_str = time_diff['formatted']
                
                # حساب التكلفة الحالية
                current_hours = time_diff['total_minutes'] / 60
                current_cost = current_hours * session.hourly_rate
                
                self.active_sessions_tree.insert('', 'end', values=(
                    session.id,
                    session.customer_name,
                    format_datetime(session.start_time),
                    duration_str,
                    format_currency(session.hourly_rate),
                    format_currency(current_cost)
                ))
                
        except Exception as e:
            show_error(f"خطأ في تحديث الجلسات النشطة: {e}")
    
    def search_sessions(self):
        """البحث في تاريخ الجلسات"""
        try:
            # مسح البيانات الحالية
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)
            
            # هنا يمكن إضافة البحث في قاعدة البيانات
            # حالياً سنعرض رسالة
            self.history_tree.insert('', 'end', values=(
                "-", "لا توجد بيانات", "-", "-", "-", "-"
            ))
            
        except Exception as e:
            show_error(f"خطأ في البحث: {e}")
    
    def end_session(self):
        """إنهاء الجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            show_warning("يرجى اختيار جلسة لإنهائها")
            return
        
        item = self.active_sessions_tree.item(selection[0])
        session_id = item['values'][0]
        
        try:
            if ask_confirmation("هل أنت متأكد من إنهاء هذه الجلسة؟"):
                session = self.db.end_session(session_id)
                if session:
                    show_success(f"تم إنهاء الجلسة بنجاح\nالمدة: {session.total_hours:.2f} ساعة\nالتكلفة: {format_currency(session.total_amount)}")
                    self.refresh_sessions()
                else:
                    show_error("لم يتم العثور على الجلسة")
        except Exception as e:
            show_error(f"خطأ في إنهاء الجلسة: {e}")
    
    def add_purchase(self):
        """إضافة مشترى للجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            show_warning("يرجى اختيار جلسة لإضافة مشترى إليها")
            return
        
        show_warning("سيتم تطوير هذه الميزة قريباً")
    
    def create_invoice(self):
        """إنشاء فاتورة للجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            show_warning("يرجى اختيار جلسة لإنشاء فاتورة لها")
            return
        
        show_warning("سيتم تطوير هذه الميزة قريباً")
    
    def update_timer(self):
        """تحديث المؤقت كل دقيقة"""
        self.refresh_active_sessions()
        self.frame.after(60000, self.update_timer)  # تحديث كل دقيقة
