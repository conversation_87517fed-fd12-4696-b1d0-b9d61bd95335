"""
دوال مساعدة لنظام إدارة مساحة العمل
Helper Functions for Work Space Manager
"""

import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime, timedelta
from typing import Any, Optional
import re

def show_success(message: str, title: str = "نجح"):
    """عرض رسالة نجاح"""
    messagebox.showinfo(title, message)

def show_error(message: str, title: str = "خطأ"):
    """عرض رسالة خطأ"""
    messagebox.showerror(title, message)

def show_warning(message: str, title: str = "تحذير"):
    """عرض رسالة تحذير"""
    messagebox.showwarning(title, message)

def show_info(message: str, title: str = "معلومات"):
    """عرض رسالة معلومات"""
    messagebox.showinfo(title, message)

def ask_confirmation(message: str, title: str = "تأكيد") -> bool:
    """طلب تأكيد من المستخدم"""
    return messagebox.askyesno(title, message)

def validate_email(email: str) -> bool:
    """التحقق من صحة البريد الإلكتروني"""
    if not email:
        return True  # البريد الإلكتروني اختياري
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone: str) -> bool:
    """التحقق من صحة رقم الهاتف"""
    if not phone:
        return True  # رقم الهاتف اختياري
    # نمط للأرقام السعودية
    pattern = r'^(05|5)[0-9]{8}$'
    return re.match(pattern, phone.replace(' ', '').replace('-', '')) is not None

def format_currency(amount: float) -> str:
    """تنسيق المبلغ كعملة"""
    return f"{amount:.2f} ريال"

def format_datetime(dt: datetime, include_time: bool = True) -> str:
    """تنسيق التاريخ والوقت"""
    if include_time:
        return dt.strftime("%Y-%m-%d %H:%M")
    else:
        return dt.strftime("%Y-%m-%d")

def format_duration(minutes: int) -> str:
    """تنسيق المدة الزمنية"""
    hours = minutes // 60
    mins = minutes % 60
    
    if hours > 0:
        return f"{hours} ساعة و {mins} دقيقة"
    else:
        return f"{mins} دقيقة"

def calculate_time_difference(start_time: datetime, end_time: datetime = None) -> dict:
    """حساب الفرق الزمني"""
    if end_time is None:
        end_time = datetime.now()
    
    diff = end_time - start_time
    total_minutes = int(diff.total_seconds() / 60)
    hours = total_minutes // 60
    minutes = total_minutes % 60
    
    return {
        'total_minutes': total_minutes,
        'hours': hours,
        'minutes': minutes,
        'formatted': format_duration(total_minutes)
    }

def create_styled_button(parent, text: str, command=None, style: str = "primary", **kwargs) -> ttk.Button:
    """إنشاء زر منسق"""
    from .config import COLORS
    
    style_config = {
        'primary': {'background': COLORS['primary'], 'foreground': 'white'},
        'secondary': {'background': COLORS['secondary'], 'foreground': 'white'},
        'danger': {'background': COLORS['danger'], 'foreground': 'white'},
        'warning': {'background': COLORS['warning'], 'foreground': 'white'},
        'success': {'background': COLORS['success'], 'foreground': 'white'},
    }
    
    button = ttk.Button(parent, text=text, command=command, **kwargs)
    return button

def create_styled_frame(parent, **kwargs) -> ttk.Frame:
    """إنشاء إطار منسق"""
    frame = ttk.Frame(parent, **kwargs)
    return frame

def create_labeled_entry(parent, label_text: str, row: int, column: int = 0, 
                        sticky: str = "ew", padx: int = 5, pady: int = 5) -> tuple:
    """إنشاء حقل إدخال مع تسمية"""
    label = ttk.Label(parent, text=label_text)
    label.grid(row=row, column=column, sticky="e", padx=padx, pady=pady)
    
    entry = ttk.Entry(parent)
    entry.grid(row=row, column=column+1, sticky=sticky, padx=padx, pady=pady)
    
    return label, entry

def create_labeled_combobox(parent, label_text: str, values: list, row: int, 
                           column: int = 0, sticky: str = "ew", padx: int = 5, pady: int = 5) -> tuple:
    """إنشاء قائمة منسدلة مع تسمية"""
    label = ttk.Label(parent, text=label_text)
    label.grid(row=row, column=column, sticky="e", padx=padx, pady=pady)
    
    combobox = ttk.Combobox(parent, values=values, state="readonly")
    combobox.grid(row=row, column=column+1, sticky=sticky, padx=padx, pady=pady)
    
    return label, combobox

def setup_treeview_columns(treeview: ttk.Treeview, columns: dict):
    """إعداد أعمدة جدول البيانات"""
    treeview['columns'] = list(columns.keys())
    treeview['show'] = 'headings'
    
    for col_id, col_info in columns.items():
        treeview.heading(col_id, text=col_info['text'])
        treeview.column(col_id, width=col_info.get('width', 100), 
                       anchor=col_info.get('anchor', 'center'))

def clear_treeview(treeview: ttk.Treeview):
    """مسح محتويات جدول البيانات"""
    for item in treeview.get_children():
        treeview.delete(item)

def get_selected_item_id(treeview: ttk.Treeview) -> Optional[int]:
    """الحصول على معرف العنصر المحدد"""
    selection = treeview.selection()
    if selection:
        item = treeview.item(selection[0])
        values = item['values']
        if values:
            return int(values[0])  # افتراض أن المعرف في العمود الأول
    return None

def center_window(window, width: int = None, height: int = None):
    """توسيط النافذة على الشاشة"""
    window.update_idletasks()
    
    if width is None:
        width = window.winfo_width()
    if height is None:
        height = window.winfo_height()
    
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    
    window.geometry(f"{width}x{height}+{x}+{y}")

def apply_rtl_support(widget):
    """تطبيق دعم الكتابة من اليمين لليسار"""
    try:
        widget.configure(justify='right')
    except:
        pass

def safe_float_convert(value: str, default: float = 0.0) -> float:
    """تحويل آمن للنص إلى رقم عشري"""
    try:
        return float(value) if value else default
    except ValueError:
        return default

def safe_int_convert(value: str, default: int = 0) -> int:
    """تحويل آمن للنص إلى رقم صحيح"""
    try:
        return int(value) if value else default
    except ValueError:
        return default

def is_valid_positive_number(value: str) -> bool:
    """التحقق من أن القيمة رقم موجب صحيح"""
    try:
        num = float(value)
        return num >= 0
    except ValueError:
        return False

def get_current_date_string() -> str:
    """الحصول على التاريخ الحالي كنص"""
    return datetime.now().strftime("%Y-%m-%d")

def get_current_datetime_string() -> str:
    """الحصول على التاريخ والوقت الحالي كنص"""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
