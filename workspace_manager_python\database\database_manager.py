"""
مدير قاعدة البيانات لنظام إدارة مساحة العمل
Database Manager for Work Space Manager
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Optional
from .models import Customer, Product, Session, Purchase, Invoice, Shift

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path: str = "data/workspace.db"):
        self.db_path = db_path
        self.ensure_data_directory()
        self.init_database()
    
    def ensure_data_directory(self):
        """التأكد من وجود مجلد البيانات"""
        data_dir = os.path.dirname(self.db_path)
        if data_dir and not os.path.exists(data_dir):
            os.makedirs(data_dir)
    
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للوصول للأعمدة بالاسم
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        with self.get_connection() as conn:
            # جدول العملاء
            conn.execute('''
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    email TEXT,
                    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    notes TEXT
                )
            ''')
            
            # جدول المنتجات
            conn.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    category TEXT,
                    price REAL DEFAULT 0,
                    cost REAL DEFAULT 0,
                    quantity INTEGER DEFAULT 0,
                    barcode TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    description TEXT
                )
            ''')
            
            # جدول الجلسات
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER,
                    customer_name TEXT,
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP,
                    hourly_rate REAL DEFAULT 0,
                    total_hours REAL DEFAULT 0,
                    total_amount REAL DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    notes TEXT,
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            ''')
            
            # جدول المشتريات
            conn.execute('''
                CREATE TABLE IF NOT EXISTS purchases (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER,
                    product_id INTEGER,
                    product_name TEXT,
                    quantity INTEGER DEFAULT 1,
                    unit_price REAL DEFAULT 0,
                    total_price REAL DEFAULT 0,
                    purchase_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    notes TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')
            
            # جدول الفواتير
            conn.execute('''
                CREATE TABLE IF NOT EXISTS invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER,
                    customer_name TEXT,
                    invoice_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    session_amount REAL DEFAULT 0,
                    purchases_amount REAL DEFAULT 0,
                    total_amount REAL DEFAULT 0,
                    discount REAL DEFAULT 0,
                    final_amount REAL DEFAULT 0,
                    payment_method TEXT DEFAULT 'نقدي',
                    notes TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions (id)
                )
            ''')
            
            # جدول الشيفتات
            conn.execute('''
                CREATE TABLE IF NOT EXISTS shifts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP,
                    opening_cash REAL DEFAULT 0,
                    closing_cash REAL DEFAULT 0,
                    total_sales REAL DEFAULT 0,
                    total_sessions INTEGER DEFAULT 0,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            conn.commit()
            self.insert_sample_data()
    
    def insert_sample_data(self):
        """إدراج بيانات تجريبية"""
        with self.get_connection() as conn:
            # التحقق من وجود بيانات
            cursor = conn.execute("SELECT COUNT(*) FROM customers")
            if cursor.fetchone()[0] == 0:
                # إضافة عملاء تجريبيين
                conn.execute('''
                    INSERT INTO customers (name, phone, email, notes) 
                    VALUES (?, ?, ?, ?)
                ''', ("أحمد محمد", "0501234567", "<EMAIL>", "عميل مميز"))
                
                conn.execute('''
                    INSERT INTO customers (name, phone, email, notes) 
                    VALUES (?, ?, ?, ?)
                ''', ("فاطمة علي", "0509876543", "<EMAIL>", "عميل جديد"))
                
                # إضافة منتجات تجريبية
                products = [
                    ("شاي", "مشروبات ساخنة", 5.0, 2.0, 50, ""),
                    ("قهوة", "مشروبات ساخنة", 8.0, 3.0, 30, ""),
                    ("عصير برتقال", "مشروبات باردة", 12.0, 5.0, 25, ""),
                    ("ساندويش", "وجبات خفيفة", 15.0, 8.0, 20, ""),
                    ("كيك", "حلويات", 20.0, 10.0, 15, "")
                ]
                
                for product in products:
                    conn.execute('''
                        INSERT INTO products (name, category, price, cost, quantity, barcode) 
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', product)
                
                conn.commit()
    
    # دوال إدارة العملاء
    def add_customer(self, customer: Customer) -> int:
        """إضافة عميل جديد"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                INSERT INTO customers (name, phone, email, notes) 
                VALUES (?, ?, ?, ?)
            ''', (customer.name, customer.phone, customer.email, customer.notes))
            return cursor.lastrowid
    
    def get_customers(self, search_term: str = "") -> List[Customer]:
        """الحصول على قائمة العملاء"""
        with self.get_connection() as conn:
            if search_term:
                cursor = conn.execute('''
                    SELECT * FROM customers 
                    WHERE name LIKE ? OR phone LIKE ? OR email LIKE ?
                    ORDER BY name
                ''', (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"))
            else:
                cursor = conn.execute('SELECT * FROM customers ORDER BY name')
            
            customers = []
            for row in cursor.fetchall():
                customer = Customer(
                    id=row['id'],
                    name=row['name'],
                    phone=row['phone'] or "",
                    email=row['email'] or "",
                    registration_date=datetime.fromisoformat(row['registration_date']),
                    is_active=bool(row['is_active']),
                    notes=row['notes'] or ""
                )
                customers.append(customer)
            return customers
    
    def update_customer(self, customer: Customer):
        """تحديث بيانات العميل"""
        with self.get_connection() as conn:
            conn.execute('''
                UPDATE customers 
                SET name=?, phone=?, email=?, notes=?, is_active=?
                WHERE id=?
            ''', (customer.name, customer.phone, customer.email, 
                  customer.notes, customer.is_active, customer.id))
    
    def delete_customer(self, customer_id: int):
        """حذف عميل"""
        with self.get_connection() as conn:
            conn.execute('DELETE FROM customers WHERE id=?', (customer_id,))
    
    # دوال إدارة المنتجات
    def add_product(self, product: Product) -> int:
        """إضافة منتج جديد"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                INSERT INTO products (name, category, price, cost, quantity, barcode, description)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (product.name, product.category, product.price, product.cost,
                  product.quantity, product.barcode, product.description))
            return cursor.lastrowid

    def get_products(self, search_term: str = "") -> List[Product]:
        """الحصول على قائمة المنتجات"""
        with self.get_connection() as conn:
            if search_term:
                cursor = conn.execute('''
                    SELECT * FROM products
                    WHERE name LIKE ? OR category LIKE ? OR barcode LIKE ?
                    ORDER BY name
                ''', (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"))
            else:
                cursor = conn.execute('SELECT * FROM products ORDER BY name')

            products = []
            for row in cursor.fetchall():
                product = Product(
                    id=row['id'],
                    name=row['name'],
                    category=row['category'] or "",
                    price=row['price'],
                    cost=row['cost'],
                    quantity=row['quantity'],
                    barcode=row['barcode'] or "",
                    is_active=bool(row['is_active']),
                    description=row['description'] or ""
                )
                products.append(product)
            return products

    def update_product(self, product: Product):
        """تحديث بيانات المنتج"""
        with self.get_connection() as conn:
            conn.execute('''
                UPDATE products
                SET name=?, category=?, price=?, cost=?, quantity=?, barcode=?, description=?, is_active=?
                WHERE id=?
            ''', (product.name, product.category, product.price, product.cost,
                  product.quantity, product.barcode, product.description,
                  product.is_active, product.id))

    def delete_product(self, product_id: int):
        """حذف منتج"""
        with self.get_connection() as conn:
            conn.execute('DELETE FROM products WHERE id=?', (product_id,))

    # دوال إدارة الجلسات
    def start_session(self, customer_id: int, customer_name: str, hourly_rate: float) -> int:
        """بدء جلسة جديدة"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                INSERT INTO sessions (customer_id, customer_name, hourly_rate)
                VALUES (?, ?, ?)
            ''', (customer_id, customer_name, hourly_rate))
            return cursor.lastrowid

    def end_session(self, session_id: int) -> Session:
        """إنهاء جلسة"""
        with self.get_connection() as conn:
            # الحصول على الجلسة
            cursor = conn.execute('SELECT * FROM sessions WHERE id=?', (session_id,))
            row = cursor.fetchone()
            if not row:
                return None

            # حساب الوقت والتكلفة
            start_time = datetime.fromisoformat(row['start_time'])
            end_time = datetime.now()
            hours = (end_time - start_time).total_seconds() / 3600
            total_amount = hours * row['hourly_rate']

            # تحديث الجلسة
            conn.execute('''
                UPDATE sessions
                SET end_time=?, total_hours=?, total_amount=?, is_active=0
                WHERE id=?
            ''', (end_time.isoformat(), round(hours, 2), round(total_amount, 2), session_id))

            # إرجاع الجلسة المحدثة
            return self.get_session(session_id)

    def get_active_sessions(self) -> List[Session]:
        """الحصول على الجلسات النشطة"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM sessions
                WHERE is_active=1
                ORDER BY start_time DESC
            ''')

            sessions = []
            for row in cursor.fetchall():
                session = Session(
                    id=row['id'],
                    customer_id=row['customer_id'],
                    customer_name=row['customer_name'],
                    start_time=datetime.fromisoformat(row['start_time']),
                    end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
                    hourly_rate=row['hourly_rate'],
                    total_hours=row['total_hours'],
                    total_amount=row['total_amount'],
                    is_active=bool(row['is_active']),
                    notes=row['notes'] or ""
                )
                sessions.append(session)
            return sessions

    def get_session(self, session_id: int) -> Optional[Session]:
        """الحصول على جلسة محددة"""
        with self.get_connection() as conn:
            cursor = conn.execute('SELECT * FROM sessions WHERE id=?', (session_id,))
            row = cursor.fetchone()
            if not row:
                return None

            return Session(
                id=row['id'],
                customer_id=row['customer_id'],
                customer_name=row['customer_name'],
                start_time=datetime.fromisoformat(row['start_time']),
                end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
                hourly_rate=row['hourly_rate'],
                total_hours=row['total_hours'],
                total_amount=row['total_amount'],
                is_active=bool(row['is_active']),
                notes=row['notes'] or ""
            )

    # دوال إدارة المشتريات
    def add_purchase(self, purchase: Purchase) -> int:
        """إضافة مشترى"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                INSERT INTO purchases (session_id, product_id, product_name, quantity, unit_price, total_price, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (purchase.session_id, purchase.product_id, purchase.product_name,
                  purchase.quantity, purchase.unit_price, purchase.total_price, purchase.notes))
            return cursor.lastrowid

    def get_session_purchases(self, session_id: int) -> List[Purchase]:
        """الحصول على مشتريات الجلسة"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM purchases
                WHERE session_id=?
                ORDER BY purchase_time
            ''', (session_id,))

            purchases = []
            for row in cursor.fetchall():
                purchase = Purchase(
                    id=row['id'],
                    session_id=row['session_id'],
                    product_id=row['product_id'],
                    product_name=row['product_name'],
                    quantity=row['quantity'],
                    unit_price=row['unit_price'],
                    total_price=row['total_price'],
                    purchase_time=datetime.fromisoformat(row['purchase_time']),
                    notes=row['notes'] or ""
                )
                purchases.append(purchase)
            return purchases

    # دوال الإحصائيات
    def get_daily_stats(self, date: datetime = None) -> dict:
        """إحصائيات يومية"""
        if date is None:
            date = datetime.now()

        date_str = date.strftime('%Y-%m-%d')

        with self.get_connection() as conn:
            # إجمالي المبيعات
            cursor = conn.execute('''
                SELECT COALESCE(SUM(total_amount), 0) as total_sales
                FROM sessions
                WHERE DATE(start_time) = ?
            ''', (date_str,))
            total_sales = cursor.fetchone()['total_sales']

            # عدد الجلسات
            cursor = conn.execute('''
                SELECT COUNT(*) as session_count
                FROM sessions
                WHERE DATE(start_time) = ?
            ''', (date_str,))
            session_count = cursor.fetchone()['session_count']

            # عدد العملاء الفريدين
            cursor = conn.execute('''
                SELECT COUNT(DISTINCT customer_id) as unique_customers
                FROM sessions
                WHERE DATE(start_time) = ?
            ''', (date_str,))
            unique_customers = cursor.fetchone()['unique_customers']

            return {
                'total_sales': total_sales,
                'session_count': session_count,
                'unique_customers': unique_customers,
                'date': date_str
            }
