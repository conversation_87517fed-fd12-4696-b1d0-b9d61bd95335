"""
مدير قاعدة البيانات لنظام إدارة مساحة العمل
Database Manager for Work Space Manager
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Optional
from .models import Customer, Product, Session, Purchase, Invoice, Shift

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path: str = "data/workspace.db"):
        self.db_path = db_path
        self.ensure_data_directory()
        self.init_database()
    
    def ensure_data_directory(self):
        """التأكد من وجود مجلد البيانات"""
        data_dir = os.path.dirname(self.db_path)
        if data_dir and not os.path.exists(data_dir):
            os.makedirs(data_dir)
    
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للوصول للأعمدة بالاسم
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        with self.get_connection() as conn:
            # جدول العملاء
            conn.execute('''
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    email TEXT,
                    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    notes TEXT
                )
            ''')
            
            # جدول المنتجات
            conn.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    category TEXT,
                    price REAL DEFAULT 0,
                    cost REAL DEFAULT 0,
                    quantity INTEGER DEFAULT 0,
                    barcode TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    description TEXT
                )
            ''')
            
            # جدول الجلسات
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER,
                    customer_name TEXT,
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP,
                    hourly_rate REAL DEFAULT 0,
                    total_hours REAL DEFAULT 0,
                    total_amount REAL DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    notes TEXT,
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            ''')
            
            # جدول المشتريات
            conn.execute('''
                CREATE TABLE IF NOT EXISTS purchases (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER,
                    product_id INTEGER,
                    product_name TEXT,
                    quantity INTEGER DEFAULT 1,
                    unit_price REAL DEFAULT 0,
                    total_price REAL DEFAULT 0,
                    purchase_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    notes TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')
            
            # جدول الفواتير
            conn.execute('''
                CREATE TABLE IF NOT EXISTS invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER,
                    customer_id INTEGER,
                    customer_name TEXT,
                    invoice_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    session_amount REAL DEFAULT 0,
                    purchases_amount REAL DEFAULT 0,
                    total_amount REAL DEFAULT 0,
                    discount REAL DEFAULT 0,
                    discount_type TEXT DEFAULT 'مبلغ',
                    final_amount REAL DEFAULT 0,
                    paid_amount REAL DEFAULT 0,
                    remaining_amount REAL DEFAULT 0,
                    payment_method TEXT DEFAULT 'نقدي',
                    payment_status TEXT DEFAULT 'غير مدفوع',
                    notes TEXT,
                    FOREIGN KEY (session_id) REFERENCES sessions (id),
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            ''')
            
            # جدول الشيفتات
            conn.execute('''
                CREATE TABLE IF NOT EXISTS shifts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_time TIMESTAMP,
                    opening_cash REAL DEFAULT 0,
                    closing_cash REAL DEFAULT 0,
                    total_sales REAL DEFAULT 0,
                    total_sessions INTEGER DEFAULT 0,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')
            
            conn.commit()
            self.insert_sample_data()
    
    def insert_sample_data(self):
        """إدراج بيانات تجريبية"""
        with self.get_connection() as conn:
            # التحقق من وجود بيانات
            cursor = conn.execute("SELECT COUNT(*) FROM customers")
            if cursor.fetchone()[0] == 0:
                # إضافة عملاء تجريبيين
                conn.execute('''
                    INSERT INTO customers (name, phone, email, notes) 
                    VALUES (?, ?, ?, ?)
                ''', ("أحمد محمد", "0501234567", "<EMAIL>", "عميل مميز"))
                
                conn.execute('''
                    INSERT INTO customers (name, phone, email, notes) 
                    VALUES (?, ?, ?, ?)
                ''', ("فاطمة علي", "0509876543", "<EMAIL>", "عميل جديد"))
                
                # إضافة منتجات تجريبية
                products = [
                    ("شاي", "مشروبات ساخنة", 5.0, 2.0, 50, ""),
                    ("قهوة", "مشروبات ساخنة", 8.0, 3.0, 30, ""),
                    ("عصير برتقال", "مشروبات باردة", 12.0, 5.0, 25, ""),
                    ("ساندويش", "وجبات خفيفة", 15.0, 8.0, 20, ""),
                    ("كيك", "حلويات", 20.0, 10.0, 15, "")
                ]
                
                for product in products:
                    conn.execute('''
                        INSERT INTO products (name, category, price, cost, quantity, barcode) 
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', product)
                
                conn.commit()
    
    # دوال إدارة العملاء
    def add_customer(self, customer: Customer) -> int:
        """إضافة عميل جديد"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                INSERT INTO customers (name, phone, email, notes) 
                VALUES (?, ?, ?, ?)
            ''', (customer.name, customer.phone, customer.email, customer.notes))
            return cursor.lastrowid
    
    def get_customers(self, search_term: str = "") -> List[Customer]:
        """الحصول على قائمة العملاء"""
        with self.get_connection() as conn:
            if search_term:
                cursor = conn.execute('''
                    SELECT * FROM customers 
                    WHERE name LIKE ? OR phone LIKE ? OR email LIKE ?
                    ORDER BY name
                ''', (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"))
            else:
                cursor = conn.execute('SELECT * FROM customers ORDER BY name')
            
            customers = []
            for row in cursor.fetchall():
                customer = Customer(
                    id=row['id'],
                    name=row['name'],
                    phone=row['phone'] or "",
                    email=row['email'] or "",
                    registration_date=datetime.fromisoformat(row['registration_date']),
                    is_active=bool(row['is_active']),
                    notes=row['notes'] or ""
                )
                customers.append(customer)
            return customers
    
    def update_customer(self, customer: Customer):
        """تحديث بيانات العميل"""
        with self.get_connection() as conn:
            conn.execute('''
                UPDATE customers 
                SET name=?, phone=?, email=?, notes=?, is_active=?
                WHERE id=?
            ''', (customer.name, customer.phone, customer.email, 
                  customer.notes, customer.is_active, customer.id))
    
    def delete_customer(self, customer_id: int):
        """حذف عميل"""
        with self.get_connection() as conn:
            conn.execute('DELETE FROM customers WHERE id=?', (customer_id,))
    
    # دوال إدارة المنتجات
    def add_product(self, product: Product) -> int:
        """إضافة منتج جديد"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                INSERT INTO products (name, category, price, cost, quantity, barcode, description)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (product.name, product.category, product.price, product.cost,
                  product.quantity, product.barcode, product.description))
            return cursor.lastrowid

    def get_products(self, search_term: str = "") -> List[Product]:
        """الحصول على قائمة المنتجات"""
        with self.get_connection() as conn:
            if search_term:
                cursor = conn.execute('''
                    SELECT * FROM products
                    WHERE name LIKE ? OR category LIKE ? OR barcode LIKE ?
                    ORDER BY name
                ''', (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"))
            else:
                cursor = conn.execute('SELECT * FROM products ORDER BY name')

            products = []
            for row in cursor.fetchall():
                product = Product(
                    id=row['id'],
                    name=row['name'],
                    category=row['category'] or "",
                    price=row['price'],
                    cost=row['cost'],
                    quantity=row['quantity'],
                    barcode=row['barcode'] or "",
                    is_active=bool(row['is_active']),
                    description=row['description'] or ""
                )
                products.append(product)
            return products

    def update_product(self, product: Product):
        """تحديث بيانات المنتج"""
        with self.get_connection() as conn:
            conn.execute('''
                UPDATE products
                SET name=?, category=?, price=?, cost=?, quantity=?, barcode=?, description=?, is_active=?
                WHERE id=?
            ''', (product.name, product.category, product.price, product.cost,
                  product.quantity, product.barcode, product.description,
                  product.is_active, product.id))

    def delete_product(self, product_id: int):
        """حذف منتج"""
        with self.get_connection() as conn:
            conn.execute('DELETE FROM products WHERE id=?', (product_id,))

    # دوال إدارة الجلسات
    def start_session(self, customer_id: int, customer_name: str, hourly_rate: float) -> int:
        """بدء جلسة جديدة"""
        try:
            with self.get_connection() as conn:
                # التحقق من وجود جلسة نشطة للعميل
                cursor = conn.execute('''
                    SELECT id FROM sessions
                    WHERE customer_id=? AND is_active=1
                ''', (customer_id,))

                existing_session = cursor.fetchone()
                if existing_session:
                    raise ValueError(f"يوجد جلسة نشطة للعميل بالفعل (رقم الجلسة: {existing_session['id']})")

                # إنشاء جلسة جديدة
                cursor = conn.execute('''
                    INSERT INTO sessions (customer_id, customer_name, hourly_rate, start_time, is_active)
                    VALUES (?, ?, ?, ?, 1)
                ''', (customer_id, customer_name, hourly_rate, datetime.now().isoformat()))

                session_id = cursor.lastrowid
                print(f"تم إنشاء جلسة جديدة: ID={session_id}, العميل={customer_name}")
                return session_id

        except Exception as e:
            print(f"خطأ في بدء الجلسة: {e}")
            raise

    def end_session(self, session_id: int) -> Session:
        """إنهاء جلسة"""
        with self.get_connection() as conn:
            # الحصول على الجلسة
            cursor = conn.execute('SELECT * FROM sessions WHERE id=?', (session_id,))
            row = cursor.fetchone()
            if not row:
                return None

            # حساب الوقت والتكلفة
            start_time = datetime.fromisoformat(row['start_time'])
            end_time = datetime.now()
            hours = (end_time - start_time).total_seconds() / 3600
            total_amount = hours * row['hourly_rate']

            # تحديث الجلسة
            conn.execute('''
                UPDATE sessions
                SET end_time=?, total_hours=?, total_amount=?, is_active=0
                WHERE id=?
            ''', (end_time.isoformat(), round(hours, 2), round(total_amount, 2), session_id))

            # إرجاع الجلسة المحدثة
            return self.get_session(session_id)

    def get_active_sessions(self) -> List[Session]:
        """الحصول على الجلسات النشطة"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM sessions
                WHERE is_active=1
                ORDER BY start_time DESC
            ''')

            sessions = []
            for row in cursor.fetchall():
                session = Session(
                    id=row['id'],
                    customer_id=row['customer_id'],
                    customer_name=row['customer_name'],
                    start_time=datetime.fromisoformat(row['start_time']),
                    end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
                    hourly_rate=row['hourly_rate'],
                    total_hours=row['total_hours'],
                    total_amount=row['total_amount'],
                    is_active=bool(row['is_active']),
                    notes=row['notes'] or ""
                )
                sessions.append(session)
            return sessions

    def get_session(self, session_id: int) -> Optional[Session]:
        """الحصول على جلسة محددة"""
        with self.get_connection() as conn:
            cursor = conn.execute('SELECT * FROM sessions WHERE id=?', (session_id,))
            row = cursor.fetchone()
            if not row:
                return None

            return Session(
                id=row['id'],
                customer_id=row['customer_id'],
                customer_name=row['customer_name'],
                start_time=datetime.fromisoformat(row['start_time']),
                end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
                hourly_rate=row['hourly_rate'],
                total_hours=row['total_hours'],
                total_amount=row['total_amount'],
                is_active=bool(row['is_active']),
                notes=row['notes'] or ""
            )

    # دوال إدارة المشتريات
    def add_purchase(self, session_id: int, product_id: int, product_name: str,
                    quantity: int, unit_price: float, notes: str = "") -> int:
        """إضافة مشترى لجلسة"""
        try:
            total_price = quantity * unit_price

            with self.get_connection() as conn:
                # التحقق من وجود الجلسة
                cursor = conn.execute('SELECT id FROM sessions WHERE id=? AND is_active=1', (session_id,))
                if not cursor.fetchone():
                    raise ValueError("الجلسة غير موجودة أو غير نشطة")

                # التحقق من وجود المنتج والكمية المتاحة
                cursor = conn.execute('SELECT quantity FROM products WHERE id=?', (product_id,))
                product_row = cursor.fetchone()
                if not product_row:
                    raise ValueError("المنتج غير موجود")

                if product_row['quantity'] < quantity:
                    raise ValueError(f"الكمية المطلوبة ({quantity}) أكبر من المتاح ({product_row['quantity']})")

                # إضافة المشترى
                cursor = conn.execute('''
                    INSERT INTO purchases (session_id, product_id, product_name, quantity, unit_price, total_price, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (session_id, product_id, product_name, quantity, unit_price, total_price, notes))

                purchase_id = cursor.lastrowid

                # تحديث كمية المنتج
                conn.execute('''
                    UPDATE products
                    SET quantity = quantity - ?
                    WHERE id = ?
                ''', (quantity, product_id))

                print(f"تم إضافة مشترى: ID={purchase_id}, المنتج={product_name}, الكمية={quantity}")
                return purchase_id

        except Exception as e:
            print(f"خطأ في إضافة المشترى: {e}")
            raise

    def get_session_purchases(self, session_id: int) -> List[Purchase]:
        """الحصول على مشتريات الجلسة"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM purchases
                WHERE session_id=?
                ORDER BY purchase_time
            ''', (session_id,))

            purchases = []
            for row in cursor.fetchall():
                purchase = Purchase(
                    id=row['id'],
                    session_id=row['session_id'],
                    product_id=row['product_id'],
                    product_name=row['product_name'],
                    quantity=row['quantity'],
                    unit_price=row['unit_price'],
                    total_price=row['total_price'],
                    purchase_time=datetime.fromisoformat(row['purchase_time']),
                    notes=row['notes'] or ""
                )
                purchases.append(purchase)
            return purchases

    # دوال الإحصائيات
    def get_daily_stats(self, date: datetime = None) -> dict:
        """إحصائيات يومية"""
        if date is None:
            date = datetime.now()

        date_str = date.strftime('%Y-%m-%d')

        with self.get_connection() as conn:
            # إجمالي المبيعات
            cursor = conn.execute('''
                SELECT COALESCE(SUM(total_amount), 0) as total_sales
                FROM sessions
                WHERE DATE(start_time) = ?
            ''', (date_str,))
            total_sales = cursor.fetchone()['total_sales']

            # عدد الجلسات
            cursor = conn.execute('''
                SELECT COUNT(*) as session_count
                FROM sessions
                WHERE DATE(start_time) = ?
            ''', (date_str,))
            session_count = cursor.fetchone()['session_count']

            # عدد العملاء الفريدين
            cursor = conn.execute('''
                SELECT COUNT(DISTINCT customer_id) as unique_customers
                FROM sessions
                WHERE DATE(start_time) = ?
            ''', (date_str,))
            unique_customers = cursor.fetchone()['unique_customers']

            return {
                'total_sales': total_sales,
                'session_count': session_count,
                'unique_customers': unique_customers,
                'date': date_str
            }

    def check_and_end_expired_sessions(self):
        """التحقق من الجلسات المنتهية وإنهاؤها تلقائياً"""
        from utils.config import DEFAULT_SESSION_HOURS, AUTO_END_SESSIONS

        if not AUTO_END_SESSIONS:
            return []

        ended_sessions = []

        with self.get_connection() as conn:
            # الحصول على الجلسات النشطة
            cursor = conn.execute('''
                SELECT * FROM sessions
                WHERE is_active=1
            ''')

            for row in cursor.fetchall():
                start_time = datetime.fromisoformat(row['start_time'])
                current_time = datetime.now()
                elapsed_hours = (current_time - start_time).total_seconds() / 3600

                # إذا تجاوزت الجلسة المدة المحددة
                if elapsed_hours >= DEFAULT_SESSION_HOURS:
                    session_id = row['id']
                    session = self.end_session(session_id)
                    if session:
                        ended_sessions.append(session)

        return ended_sessions

    def get_completed_sessions(self) -> List[Session]:
        """الحصول على الجلسات المنتهية"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM sessions
                WHERE is_active=0
                ORDER BY end_time DESC
            ''')

            sessions = []
            for row in cursor.fetchall():
                session = Session(
                    id=row['id'],
                    customer_id=row['customer_id'],
                    customer_name=row['customer_name'],
                    start_time=datetime.fromisoformat(row['start_time']),
                    end_time=datetime.fromisoformat(row['end_time']) if row['end_time'] else None,
                    hourly_rate=row['hourly_rate'],
                    total_hours=row['total_hours'],
                    total_amount=row['total_amount'],
                    is_active=False,
                    notes=row['notes'] or ""
                )
                sessions.append(session)
            return sessions

    # دوال إدارة الفواتير
    def create_invoice(self, session_id: int, discount: float = 0, discount_type: str = "مبلغ",
                      paid_amount: float = 0, payment_method: str = "نقدي", notes: str = "") -> int:
        """إنشاء فاتورة جديدة"""
        with self.get_connection() as conn:
            # الحصول على بيانات الجلسة
            session = self.get_session(session_id)
            if not session:
                raise ValueError("الجلسة غير موجودة")

            # حساب مبلغ المشتريات
            purchases = self.get_session_purchases(session_id)
            purchases_amount = sum(p.total_price for p in purchases)

            # إنشاء الفاتورة
            from .models import Invoice
            invoice = Invoice(
                session_id=session_id,
                customer_id=session.customer_id,
                customer_name=session.customer_name,
                session_amount=session.total_amount,
                purchases_amount=purchases_amount,
                discount=discount,
                discount_type=discount_type,
                paid_amount=paid_amount,
                payment_method=payment_method,
                notes=notes
            )

            cursor = conn.execute('''
                INSERT INTO invoices (
                    session_id, customer_id, customer_name, session_amount,
                    purchases_amount, total_amount, discount, discount_type,
                    final_amount, paid_amount, remaining_amount,
                    payment_method, payment_status, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                invoice.session_id, invoice.customer_id, invoice.customer_name,
                invoice.session_amount, invoice.purchases_amount, invoice.total_amount,
                invoice.discount, invoice.discount_type, invoice.final_amount,
                invoice.paid_amount, invoice.remaining_amount, invoice.payment_method,
                invoice.payment_status, invoice.notes
            ))

            return cursor.lastrowid

    def get_invoices(self, customer_id: int = None) -> List:
        """الحصول على قائمة الفواتير"""
        with self.get_connection() as conn:
            if customer_id:
                cursor = conn.execute('''
                    SELECT * FROM invoices
                    WHERE customer_id=?
                    ORDER BY invoice_date DESC
                ''', (customer_id,))
            else:
                cursor = conn.execute('''
                    SELECT * FROM invoices
                    ORDER BY invoice_date DESC
                ''')

            invoices = []
            for row in cursor.fetchall():
                from .models import Invoice
                invoice = Invoice(
                    id=row['id'],
                    session_id=row['session_id'],
                    customer_id=row['customer_id'],
                    customer_name=row['customer_name'],
                    invoice_date=datetime.fromisoformat(row['invoice_date']),
                    session_amount=row['session_amount'],
                    purchases_amount=row['purchases_amount'],
                    total_amount=row['total_amount'],
                    discount=row['discount'],
                    discount_type=row['discount_type'],
                    final_amount=row['final_amount'],
                    paid_amount=row['paid_amount'],
                    remaining_amount=row['remaining_amount'],
                    payment_method=row['payment_method'],
                    payment_status=row['payment_status'],
                    notes=row['notes'] or ""
                )
                invoices.append(invoice)
            return invoices

    def update_invoice_payment(self, invoice_id: int, paid_amount: float, payment_method: str = None):
        """تحديث دفعة الفاتورة"""
        with self.get_connection() as conn:
            # الحصول على الفاتورة الحالية
            cursor = conn.execute('SELECT * FROM invoices WHERE id=?', (invoice_id,))
            row = cursor.fetchone()
            if not row:
                raise ValueError("الفاتورة غير موجودة")

            # حساب المبالغ الجديدة
            final_amount = row['final_amount']
            new_paid_amount = paid_amount
            remaining_amount = final_amount - new_paid_amount

            # تحديث حالة الدفع
            if new_paid_amount >= final_amount:
                payment_status = "مدفوع"
                remaining_amount = 0
            elif new_paid_amount > 0:
                payment_status = "مدفوع جزئياً"
            else:
                payment_status = "غير مدفوع"

            # تحديث الفاتورة
            update_query = '''
                UPDATE invoices
                SET paid_amount=?, remaining_amount=?, payment_status=?
            '''
            params = [new_paid_amount, remaining_amount, payment_status]

            if payment_method:
                update_query += ', payment_method=?'
                params.append(payment_method)

            update_query += ' WHERE id=?'
            params.append(invoice_id)

            conn.execute(update_query, params)



    def get_session_purchases(self, session_id: int) -> List[Purchase]:
        """الحصول على مشتريات جلسة"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                SELECT * FROM purchases
                WHERE session_id=?
                ORDER BY purchase_date DESC
            ''', (session_id,))

            purchases = []
            for row in cursor.fetchall():
                purchase = Purchase(
                    id=row['id'],
                    session_id=row['session_id'],
                    product_id=row['product_id'],
                    product_name=row['product_name'],
                    quantity=row['quantity'],
                    unit_price=row['unit_price'],
                    total_price=row['total_price'],
                    purchase_date=datetime.fromisoformat(row['purchase_date']),
                    notes=row['notes'] or ""
                )
                purchases.append(purchase)
            return purchases

    def delete_purchase(self, purchase_id: int):
        """حذف مشترى"""
        with self.get_connection() as conn:
            # الحصول على بيانات المشترى أولاً
            cursor = conn.execute('SELECT * FROM purchases WHERE id=?', (purchase_id,))
            purchase = cursor.fetchone()

            if purchase:
                # إعادة الكمية للمنتج
                conn.execute('''
                    UPDATE products
                    SET quantity = quantity + ?
                    WHERE id = ?
                ''', (purchase['quantity'], purchase['product_id']))

                # حذف المشترى
                conn.execute('DELETE FROM purchases WHERE id=?', (purchase_id,))

    def delete_session(self, session_id: int):
        """حذف جلسة مع جميع المشتريات المرتبطة بها"""
        with self.get_connection() as conn:
            # حذف المشتريات المرتبطة بالجلسة أولاً
            cursor = conn.execute('SELECT * FROM purchases WHERE session_id=?', (session_id,))
            purchases = cursor.fetchall()

            # إعادة الكميات للمنتجات
            for purchase in purchases:
                conn.execute('''
                    UPDATE products
                    SET quantity = quantity + ?
                    WHERE id = ?
                ''', (purchase['quantity'], purchase['product_id']))

            # حذف المشتريات
            conn.execute('DELETE FROM purchases WHERE session_id=?', (session_id,))

            # حذف الجلسة
            conn.execute('DELETE FROM sessions WHERE id=?', (session_id,))

    def update_session(self, session_id: int, customer_name: str = None, hourly_rate: float = None, notes: str = None):
        """تحديث بيانات الجلسة"""
        with self.get_connection() as conn:
            updates = []
            params = []

            if customer_name is not None:
                updates.append('customer_name=?')
                params.append(customer_name)

            if hourly_rate is not None:
                updates.append('hourly_rate=?')
                params.append(hourly_rate)

                # إعادة حساب المبلغ الإجمالي
                cursor = conn.execute('SELECT start_time, end_time FROM sessions WHERE id=?', (session_id,))
                session_data = cursor.fetchone()

                if session_data and session_data['end_time']:
                    start_time = datetime.fromisoformat(session_data['start_time'])
                    end_time = datetime.fromisoformat(session_data['end_time'])
                    total_hours = (end_time - start_time).total_seconds() / 3600
                    total_amount = total_hours * hourly_rate

                    updates.append('total_hours=?')
                    updates.append('total_amount=?')
                    params.extend([total_hours, total_amount])

            if notes is not None:
                updates.append('notes=?')
                params.append(notes)

            if updates:
                params.append(session_id)
                query = f"UPDATE sessions SET {', '.join(updates)} WHERE id=?"
                conn.execute(query, params)
